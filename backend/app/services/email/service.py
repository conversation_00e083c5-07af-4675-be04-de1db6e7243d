from typing import Optional
import os
from pathlib import Path

import httpx
from app.core.config import settings
from app.core.logging import get_logger
from app.services.email.interface import IEmailService
from fastapi import HTTPException, status
from fastapi_mail import ConnectionConfig, FastMail, MessageSchema

logger = get_logger(__name__)


class EmailService(IEmailService):
    def __init__(self):
        self.use_resend = settings.USE_RESEND and settings.RESEND_API_KEY
        self.templates_dir = Path(__file__).parent.parent.parent / "templates"

        if not self.use_resend:
            # Initialize FastMail for SMTP
            self.config = ConnectionConfig(
                MAIL_USERNAME=settings.SMTP_USERNAME,
                MAIL_PASSWORD=settings.SMTP_PASSWORD,
                MAIL_FROM=settings.SMTP_FROM,
                MAIL_PORT=settings.SMTP_PORT,
                MAIL_SERVER=settings.SMTP_HOST,
                MAIL_FROM_NAME=settings.SMTP_FROM_NAME,
                MAIL_STARTTLS=True,
                MAIL_SSL_TLS=False,
                USE_CREDENTIALS=True,
                VALIDATE_CERTS=True,
            )
            self.fastmail = FastMail(self.config)
        else:
            self.fastmail = None
            logger.info("Using Resend for email delivery")

    def _load_template(self, template_name: str, **kwargs) -> str:
        """Load and render HTML email template."""
        try:
            template_path = self.templates_dir / f"{template_name}.html"
            if not template_path.exists():
                logger.warning(f"Template {template_name}.html not found, using fallback")
                return self._get_fallback_template(template_name, **kwargs)

            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()

            # Simple template variable replacement
            for key, value in kwargs.items():
                template_content = template_content.replace(f"{{{{{key}}}}}", str(value))

            return template_content
        except Exception as e:
            logger.error(f"Error loading template {template_name}: {str(e)}")
            return self._get_fallback_template(template_name, **kwargs)

    def _get_fallback_template(self, template_name: str, **kwargs) -> str:
        """Get fallback template if main template fails to load."""
        if template_name == "forgot_password":
            return f"""
            <html>
                <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h1>Reset Your Password</h1>
                    <p>Click the link below to reset your password:</p>
                    <p><a href="{kwargs.get('reset_url', '#')}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                    <p>If you didn't request this, please ignore this email.</p>
                    <p>This link will expire in 2 hours.</p>
                </body>
            </html>
            """
        elif template_name == "invite":
            return f"""
            <html>
                <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h1>You're Invited to TractionX!</h1>
                    <p>You've been invited to join TractionX. Click the link below to accept:</p>
                    <p><a href="{kwargs.get('accept_url', '#')}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Accept Invitation</a></p>
                    <p>This invitation will expire in 7 days.</p>
                </body>
            </html>
            """
        else:
            return f"""
            <html>
                <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h1>TractionX</h1>
                    <p>This is a message from TractionX.</p>
                </body>
            </html>
            """

    async def _send_via_resend(self, to: str, subject: str, html_content: str) -> None:
        """Send email via Resend API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.resend.com/emails",
                    headers={
                        "Authorization": f"Bearer {settings.RESEND_API_KEY}",
                        "Content-Type": "application/json",
                    },
                    json={
                        "from": f"TractionX <outreach@{settings.RESEND_DOMAIN}>",
                        "to": [to],
                        "subject": subject,
                        "html": html_content,
                    },
                )

                if response.status_code != 200:
                    logger.error(
                        f"Resend API error: {response.status_code} - {response.text}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Failed to send email via Resend",
                    )

                logger.info(f"Email sent via Resend to {to}")

        except httpx.RequestError as e:
            logger.error(f"Resend API request error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send email via Resend",
            )

    async def _send_email(self, to: str, subject: str, html_content: str) -> None:
        """Send email using configured service (Resend or SMTP)."""
        if self.use_resend:
            await self._send_via_resend(to, subject, html_content)
        else:
            # Use FastMail/SMTP
            message = MessageSchema(
                subject=subject, recipients=[to], body=html_content, subtype="html"
            )
            await self.fastmail.send_message(message)
            logger.info(f"Email sent via SMTP to {to}")

    async def send_magic_link_email(
        self,
        email: str,
        magic_link_url: str,
        investor_name: Optional[str] = None,
        form_name: Optional[str] = None,
    ) -> None:
        """Send magic link email for shared form access."""
        try:
            subject = (
                f"Access Your Form{f' from {investor_name}' if investor_name else ''}"
            )

            html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h1 style="color: #2563eb;">Access Your Form</h1>
                        {f"<p>You have been invited by <strong>{investor_name}</strong> to fill out a form.</p>" if investor_name else ""}
                        {f"<p><strong>Form:</strong> {form_name}</p>" if form_name else ""}
                        <p>Click the link below to access the form:</p>
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{magic_link_url}"
                               style="background-color: #2563eb; color: white; padding: 12px 24px;
                                      text-decoration: none; border-radius: 6px; display: inline-block;">
                                Access Form
                            </a>
                        </div>
                        <p style="color: #666; font-size: 14px;">
                            This link will expire in 15 minutes for security reasons.
                        </p>
                        <p style="color: #666; font-size: 14px;">
                            If you didn't request this, please ignore this email.
                        </p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                        <p style="color: #999; font-size: 12px; text-align: center;">
                            Powered by TractionX
                        </p>
                    </div>
                </body>
            </html>
            """

            await self._send_email(email, subject, html_content)
            logger.info(f"Magic link email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send magic link email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send magic link email",
            )

    async def send_password_reset_email(self, email: str, token: str) -> None:
        """Send password reset email using beautiful HTML template."""
        try:
            reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"

            # Load the beautiful template
            html_content = self._load_template(
                "forgot_password",
                email=email,
                reset_url=reset_url
            )

            subject = "Oops! Forgot Something? 🤦‍♂️ - TractionX Password Reset"

            await self._send_email(email, subject, html_content)
            logger.info(f"Password reset email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send password reset email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send password reset email",
            )

    async def send_invitation_email(
        self,
        email: str,
        token: str,
        invited_by_name: str = "TractionX Team",
        organization_name: str = "TractionX",
        role_name: str = "Team Member"
    ) -> None:
        """Send user invitation email using beautiful HTML template."""
        try:
            accept_url = f"{settings.FRONTEND_URL}/accept-invite?token={token}"

            # Load the beautiful template
            html_content = self._load_template(
                "invite",
                email=email,
                accept_url=accept_url,
                invited_by_name=invited_by_name,
                organization_name=organization_name,
                role_name=role_name
            )

            subject = f"🎉 You're Invited to Join {organization_name} on TractionX!"

            await self._send_email(email, subject, html_content)
            logger.info(f"Invitation email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send invitation email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send invitation email",
            )

    async def send_welcome_email(self, email: str, name: Optional[str] = None) -> None:
        """Send welcome email to new user."""
        try:
            message = MessageSchema(
                subject="Welcome to TractionX",
                recipients=[email],
                body=f"""
                <html>
                    <body>
                        <h1>Welcome to TractionX</h1>
                        <p>Hi {name or "there"},</p>
                        <p>Welcome to TractionX! We're excited to have you on board.</p>
                        <p>Get started by exploring our platform and features.</p>
                    </body>
                </html>
                """,
                subtype="html",
            )
            await self.fastmail.send_message(message)
            logger.info(f"Welcome email sent to {email}")
        except Exception as e:
            logger.error(f"Failed to send welcome email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send welcome email",
            )

    async def send_organization_invitation(
        self, email: str, org_name: str, token: str
    ) -> None:
        """Send organization invitation email."""
        try:
            invite_url = f"{settings.FRONTEND_URL}/accept-org-invitation?token={token}"
            message = MessageSchema(
                subject=f"Invitation to Join {org_name}",
                recipients=[email],
                body=f"""
                <html>
                    <body>
                        <h1>Join {org_name} on TractionX</h1>
                        <p>You've been invited to join {org_name} on TractionX. Click the link below to accept the invitation:</p>
                        <p><a href="{invite_url}">Accept Invitation</a></p>
                        <p>This link will expire in {settings.INVITATION_TOKEN_EXPIRY // 3600} hours.</p>
                    </body>
                </html>
                """,
                subtype="html",
            )
            await self.fastmail.send_message(message)
            logger.info(f"Organization invitation email sent to {email}")
        except Exception as e:
            logger.error(
                f"Failed to send organization invitation email to {email}: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send organization invitation email",
            )

    async def send_invite_code_email(
        self,
        email: str,
        invite_code: str,
        onboard_url: str,
        org_name: Optional[str] = None
    ) -> bool:
        """Send invite code email to user."""
        try:
            subject = "🚀 You've been invited to unlock TractionX"

            html_content = f"""
            <html>
                <body style="font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                           line-height: 1.6; color: #0f172a; background: #ffffff; margin: 0; padding: 0;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px;">
                        <!-- Header with Logo -->
                        <div style="text-align: center; margin-bottom: 40px;">
                            <h1 style="font-size: 32px; font-weight: 700; color: #0f172a; margin: 0; letter-spacing: -0.02em;">
                                TractionX
                            </h1>
                            <p style="color: #64748b; margin: 8px 0 0 0; font-size: 16px;">
                                The world's first Agentic OS for investors
                            </p>
                        </div>

                        <!-- Main Content -->
                        <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px);
                                  border-radius: 24px; padding: 40px; border: 1px solid rgba(0, 0, 0, 0.05);
                                  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">

                            <h2 style="font-size: 24px; font-weight: 600; color: #0f172a; margin: 0 0 24px 0;
                                     text-align: center; letter-spacing: -0.01em;">
                                You've been invited to unlock TractionX
                            </h2>

                            {f"<p style='font-size: 16px; color: #475569; margin: 0 0 24px 0; text-align: center;'>Create your organization: <strong>{org_name}</strong></p>" if org_name else ""}

                            <p style="font-size: 16px; color: #475569; margin: 0 0 32px 0; text-align: center;">
                                Your exclusive invite code: <strong style="font-family: 'SF Mono', Monaco, monospace;
                                background: #f1f5f9; padding: 8px 12px; border-radius: 8px; color: #0f172a;
                                font-size: 18px; letter-spacing: 2px;">{invite_code}</strong>
                            </p>

                            <!-- CTA Button -->
                            <div style="text-align: center; margin: 40px 0;">
                                <a href="{onboard_url}"
                                   style="display: inline-block; background: #0f172a; color: #ffffff;
                                        padding: 16px 32px; text-decoration: none; border-radius: 16px;
                                        font-weight: 600; font-size: 16px; letter-spacing: -0.01em;
                                        transition: all 0.15s ease;">
                                    Begin Setup
                                </a>
                            </div>

                            <p style="font-size: 14px; color: #64748b; margin: 32px 0 0 0; text-align: center;">
                                This invite code is single-use and will expire in 30 days.
                            </p>
                        </div>

                        <!-- Footer -->
                        <div style="text-align: center; margin-top: 40px;">
                            <p style="font-size: 12px; color: #94a3b8; margin: 0;">
                                Built with ❤️ for the future of investing
                            </p>
                        </div>
                    </div>
                </body>
            </html>
            """

            await self._send_email(email, subject, html_content)
            logger.info(f"Invite code email sent to {email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send invite code email to {email}: {str(e)}")
            return False

    async def send_peer_invite_email(
        self,
        email: str,
        org_name: str,
        invited_by_name: str,
        invite_token: str,
        message: Optional[str] = None
    ) -> bool:
        """Send peer invitation email."""
        try:
            accept_url = f"{settings.FRONTEND_URL}/accept-invite?token={invite_token}"
            subject = f"🎉 You've been invited to join {org_name} on TractionX"

            html_content = f"""
            <html>
                <body style="font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                           line-height: 1.6; color: #0f172a; background: #ffffff; margin: 0; padding: 0;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px;">
                        <!-- Header with Logo -->
                        <div style="text-align: center; margin-bottom: 40px;">
                            <h1 style="font-size: 32px; font-weight: 700; color: #0f172a; margin: 0; letter-spacing: -0.02em;">
                                TractionX
                            </h1>
                            <p style="color: #64748b; margin: 8px 0 0 0; font-size: 16px;">
                                The world's first Agentic OS for investors
                            </p>
                        </div>

                        <!-- Main Content -->
                        <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px);
                                  border-radius: 24px; padding: 40px; border: 1px solid rgba(0, 0, 0, 0.05);
                                  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">

                            <h2 style="font-size: 24px; font-weight: 600; color: #0f172a; margin: 0 0 24px 0;
                                     text-align: center; letter-spacing: -0.01em;">
                                Join {org_name} on TractionX
                            </h2>

                            <p style="font-size: 16px; color: #475569; margin: 0 0 24px 0; text-align: center;">
                                <strong>{invited_by_name}</strong> has invited you to join their team.
                            </p>

                            {f'<div style="background: #f8fafc; border-radius: 12px; padding: 20px; margin: 24px 0;"><p style="font-size: 14px; color: #475569; margin: 0; font-style: italic;">"{message}"</p></div>' if message else ''}

                            <!-- CTA Button -->
                            <div style="text-align: center; margin: 40px 0;">
                                <a href="{accept_url}"
                                   style="display: inline-block; background: #0f172a; color: #ffffff;
                                        padding: 16px 32px; text-decoration: none; border-radius: 16px;
                                        font-weight: 600; font-size: 16px; letter-spacing: -0.01em;
                                        transition: all 0.15s ease;">
                                    Accept Invite
                                </a>
                            </div>

                            <p style="font-size: 14px; color: #64748b; margin: 32px 0 0 0; text-align: center;">
                                This invitation will expire in 7 days.
                            </p>
                        </div>

                        <!-- Footer -->
                        <div style="text-align: center; margin-top: 40px;">
                            <p style="font-size: 12px; color: #94a3b8; margin: 0;">
                                Built with ❤️ for the future of investing
                            </p>
                        </div>
                    </div>
                </body>
            </html>
            """

            await self._send_email(email, subject, html_content)
            logger.info(f"Peer invite email sent to {email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send peer invite email to {email}: {str(e)}")
            return False


email_service = EmailService()
