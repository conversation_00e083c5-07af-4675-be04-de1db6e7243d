from abc import ABC, abstractmethod
from typing import Optional


class IEmailService(ABC):
    @abstractmethod
    async def send_password_reset_email(self, email: str, token: str) -> None:
        """Send password reset email."""
        pass

    @abstractmethod
    async def send_invitation_email(self, email: str, token: str) -> None:
        """Send user invitation email."""
        pass

    @abstractmethod
    async def send_welcome_email(self, email: str, name: Optional[str] = None) -> None:
        """Send welcome email to new user."""
        pass

    @abstractmethod
    async def send_organization_invitation(self, email: str, org_name: str, token: str) -> None:
        """Send organization invitation email."""
        pass

    @abstractmethod
    async def send_magic_link_email(
        self,
        email: str,
        magic_link_url: str,
        investor_name: Optional[str] = None,
        form_name: Optional[str] = None
    ) -> None:
        """Send magic link email for shared form access."""
        pass

    @abstractmethod
    async def send_invite_code_email(
        self,
        email: str,
        invite_code: str,
        onboard_url: str,
        org_name: Optional[str] = None
    ) -> bool:
        """Send invite code email to user."""
        pass

    @abstractmethod
    async def send_peer_invite_email(
        self,
        email: str,
        org_name: str,
        invited_by_name: str,
        invite_token: str,
        message: Optional[str] = None
    ) -> bool:
        """Send peer invitation email."""
        pass
