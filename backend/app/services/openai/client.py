"""
OpenAI API Client

Handles chat completions with OpenAI GPT models for Chat mode.
Provides fast, context-aware, human-like responses.
"""

import time
from typing import Any, Dict, List, Optional
import httpx
from datetime import datetime, timezone

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class OpenAIError(Exception):
    """Base exception for OpenAI API errors."""
    pass


class OpenAIRateLimitError(OpenAIError):
    """Raised when rate limit is exceeded."""
    pass


class OpenAITimeoutError(OpenAIError):
    """Raised when request times out."""
    pass


class OpenAIClient:
    """
    Client for OpenAI Chat Completions API.
    Handles fast, context-aware responses for Chat mode.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or getattr(settings, 'OPENAI_API_KEY', None)
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        self.base_url = "https://api.openai.com/v1"
        self.timeout = 30.0
        
        # Default headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        Create a chat completion request.
        
        Returns:
            completion_data: Full completion response
        """
        start_time = time.time()
        
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
            "presence_penalty": 0,
            "frequency_penalty": 0
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code == 429:
                    raise OpenAIRateLimitError("Rate limit exceeded. Please try again later.")
                elif response.status_code == 400:
                    error_detail = response.text
                    raise OpenAIError(f"Bad request: {error_detail}")
                elif response.status_code == 401:
                    raise OpenAIError("Invalid API key")
                elif response.status_code == 403:
                    raise OpenAIError("Access forbidden. Check your API permissions.")
                elif response.status_code >= 500:
                    raise OpenAIError(f"OpenAI server error ({response.status_code}). Please try again.")
                elif response.status_code >= 400:
                    error_detail = response.text
                    raise OpenAIError(f"API error {response.status_code}: {error_detail}")
                
                result = response.json()
                
                if not result.get("choices"):
                    raise OpenAIError("No response choices returned from OpenAI API")
                
                # Add performance metadata
                response_time_ms = int((time.time() - start_time) * 1000)
                result["_performance"] = {
                    "response_time_ms": response_time_ms,
                    "model": model
                }
                
                logger.info(f"Completed OpenAI chat completion with model: {model} in {response_time_ms}ms")
                return result
                
        except httpx.TimeoutException:
            raise OpenAITimeoutError("Request timed out. Please try again.")
        except httpx.RequestError as e:
            logger.error(f"Network error during completion: {e}")
            raise OpenAIError("Network error. Please check your connection and try again.")
        except Exception as e:
            logger.error(f"Error creating chat completion: {e}")
            if isinstance(e, (OpenAIError, OpenAIRateLimitError, OpenAITimeoutError)):
                raise
            raise OpenAIError(f"Failed to create completion: {str(e)}")
    
    def extract_content_from_completion(self, completion_data: Dict[str, Any]) -> str:
        """
        Extract content from OpenAI completion response.
        """
        try:
            choices = completion_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                return content
            
            return "No response generated"
            
        except Exception as e:
            logger.error(f"Error extracting content from completion: {e}")
            return "Error processing response"
    
    def extract_performance_metadata(self, completion_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract performance metadata from completion response.
        """
        try:
            performance = completion_data.get("_performance", {})
            usage = completion_data.get("usage", {})
            
            return {
                "response_time_ms": performance.get("response_time_ms"),
                "token_count": usage.get("total_tokens"),
                "model": performance.get("model")
            }
            
        except Exception as e:
            logger.error(f"Error extracting performance metadata: {e}")
            return {}
    
    def build_investment_prompt(
        self, 
        user_message: str, 
        deal_context: Optional[Dict[str, Any]] = None,
        chat_history: Optional[List[Dict[str, str]]] = None,
        mode: str = "chat"
    ) -> List[Dict[str, str]]:
        """
        Build a context-aware prompt for investment analysis in Chat mode.
        """
        messages = []
        
        # System message for Chat mode - human-like, fast, conversational
        system_content = """You are Orbit, TractionX's AI investment assistant. You provide fast, human-like, and context-aware responses to help investors and founders.

Key guidelines for Chat mode:
- Be conversational, helpful, and human-like in your responses
- Focus on speed and clarity over exhaustive research
- Provide strategic insights and practical advice
- Use your knowledge to give informed opinions and guidance
- Be concise but thorough when needed
- If you need sources for specific claims, suggest switching to Deep Research mode
- Always maintain a professional but approachable tone"""
        
        # Add deal context if provided
        if deal_context:
            company_name = deal_context.get("company_name", "this company")
            sector = deal_context.get("sector", "")
            stage = deal_context.get("stage", "")
            
            system_content += f"\n\nCurrent deal context:\n"
            system_content += f"- Company: {company_name}\n"
            if sector:
                system_content += f"- Sector: {sector}\n"
            if stage:
                system_content += f"- Stage: {stage}\n"
            
            system_content += "\nUse this context to provide relevant, specific advice."
        
        messages.append({"role": "system", "content": system_content})
        
        # Add chat history if provided (last 8 messages for context)
        if chat_history:
            messages.extend(chat_history[-8:])
        
        # Add current user message
        messages.append({"role": "user", "content": user_message})
        
        return messages
