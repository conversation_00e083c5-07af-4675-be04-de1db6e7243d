"""
Comprehensive Thesis Scoring Engine

This module implements the complete thesis scoring logic as specified in the PRD.
Handles all question types, aggregation methods, bonus scoring, and AI evaluation.
"""

import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, List, Union

from app.core.logging import get_logger
from app.models.form import QuestionType
from app.models.thesis import (
    AggregationType,
    CompoundCondition,
    ConditionOperator,
    FilterCondition,
    LogicalOperator,
    RuleType,
    ScoringRule,
)
from app.services.ai.text_scoring import get_text_scoring_service

logger = get_logger(__name__)


class ComprehensiveThesisScoringEngine:
    """
    Comprehensive scoring engine that implements all PRD requirements.

    Features:
    - All question types (text, numeric, select, boolean, date, file)
    - AI-powered text scoring with OpenAI
    - Repeatable section aggregation
    - Bonus scoring logic
    - Modular result storage
    """

    def __init__(self):
        """Initialize the scoring engine."""
        self.text_scoring_service = None

    async def _get_text_scoring_service(self):
        """Lazy load text scoring service."""
        if self.text_scoring_service is None:
            self.text_scoring_service = await get_text_scoring_service()
        return self.text_scoring_service

    async def calculate_comprehensive_score(
        self,
        thesis_with_rules,
        form_responses: Dict[str, Any],
        form_details=None,
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive thesis score following PRD specifications.

        Args:
            thesis_with_rules: Thesis object with expanded rules
            form_responses: User's form responses
            form_details: Form structure with questions and sections

        Returns:
            Comprehensive scoring result with modular structure
        """
        try:
            # Initialize scoring structure
            scoring_result = {
                "thesis": {
                    "thesis_id": str(thesis_with_rules.id),
                    "thesis_name": thesis_with_rules.name,
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "max_possible_score": 0.0,
                    "question_scores": {},
                    "bonus_scores": {},
                    "scoring_details": [],
                },
                "founders": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "ai_analysis": "",
                    "key_insights": [],
                },
                "market": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "ai_analysis": "",
                    "key_insights": [],
                },
                "metadata": {
                    "scoring_version": "v2.0",
                    "scored_at": int(datetime.now(timezone.utc).timestamp()),
                    "total_rules_processed": 0,
                    "ai_scoring_used": False,
                },
            }

            # Process scoring rules
            total_score = 0.0
            max_possible_score = 0.0
            ai_scoring_used = False

            for rule in thesis_with_rules.scoring_rules:
                if rule.is_deleted or rule.rule_type != RuleType.SCORING:
                    continue

                try:
                    # Calculate rule score
                    question_meta = None
                    if form_details:
                        for section in form_details.sections:  # type: ignore
                            for question in section.questions:  # type: ignore
                                if str(question.id) == str(rule.question_id):
                                    question_meta = question
                                    break

                    rule_result = await self._calculate_rule_score(
                        rule, form_responses, form_details
                    )

                    if rule_result["ai_used"]:
                        ai_scoring_used = True

                    # Apply weight
                    weighted_score = rule_result["score"] * rule.weight
                    total_score += weighted_score
                    max_possible_score += rule.weight

                    # Store question score
                    question_id = str(rule.question_id)

                    scoring_result["thesis"]["question_scores"][question_id] = {
                        "rule_id": str(rule.id),
                        "question_id": question_id,
                        "question_type": question_meta.type.value
                        if question_meta
                        else "unknown",
                        "question_label": question_meta.label
                        if question_meta
                        else "unknown",
                        "raw_score": rule_result["score"],
                        "weight": rule.weight,
                        "weighted_score": weighted_score,
                        "explanation": rule_result.get("explanation", ""),
                        "sources": rule_result.get("sources", []),
                        "ai_generated": rule_result.get("ai_generated", False),
                        "aggregation_used": rule_result.get("aggregation_used", False),
                        "aggregation_type": rule.aggregation.value
                        if rule.aggregation
                        else None,
                    }

                    # Add to scoring details
                    scoring_result["thesis"]["scoring_details"].append({
                        "rule_id": str(rule.id),
                        "question_id": question_id,
                        "score": rule_result["score"],
                        "weight": rule.weight,
                        "weighted_score": weighted_score,
                        "explanation": rule_result.get("explanation", ""),
                    })

                    scoring_result["metadata"]["total_rules_processed"] += 1

                except Exception as e:
                    logger.error(
                        f"Error processing scoring rule {rule.id}: {str(e)}",
                        exc_info=True,
                    )
                    continue

            # Process bonus rules
            for rule in thesis_with_rules.scoring_rules:
                if rule.is_deleted or rule.rule_type != RuleType.BONUS:
                    continue

                try:
                    if rule.bonus_points and await self._evaluate_condition(
                        rule.condition, form_responses
                    ):
                        bonus_id = f"bonus_{rule.id}"
                        scoring_result["thesis"]["bonus_scores"][bonus_id] = {
                            "rule_id": str(rule.id),
                            "bonus_points": rule.bonus_points,
                            "explanation": rule.notes or "Bonus condition met",
                        }
                        total_score += rule.bonus_points

                except Exception as e:
                    logger.error(
                        f"Error processing bonus rule {rule.id}: {str(e)}",
                        exc_info=True,
                    )
                    continue

            # Calculate normalized score
            normalized_score = 0.0
            if max_possible_score > 0:
                normalized_score = min(100.0, (total_score / max_possible_score) * 100)

            # Update thesis scoring
            scoring_result["thesis"]["total_score"] = total_score
            scoring_result["thesis"]["normalized_score"] = normalized_score
            scoring_result["thesis"]["max_possible_score"] = max_possible_score
            scoring_result["metadata"]["ai_scoring_used"] = ai_scoring_used

            # Generate AI analysis for founders and market (placeholder for now)
            scoring_result["founders"] = await self._generate_founder_analysis(
                form_responses
            )
            scoring_result["market"] = await self._generate_market_analysis(
                form_responses
            )

            return scoring_result

        except Exception as e:
            logger.error(f"Error in comprehensive scoring: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "thesis": {"normalized_score": 0.0},
                "founders": {"normalized_score": 0.0},
                "market": {"normalized_score": 0.0},
            }

    async def _calculate_rule_score(
        self,
        rule: ScoringRule,
        form_responses: Dict[str, Any],
        form_details=None,
    ) -> Dict[str, Any]:
        """Calculate score for a single scoring rule."""
        try:
            # Handle repeatable section aggregation
            if (
                rule.section_id
                and rule.aggregation
                and rule.aggregation != AggregationType.NONE
            ):
                return await self._calculate_aggregated_score(
                    rule, form_responses, form_details
                )

            # Handle regular scoring rule
            if not rule.question_id:
                return {
                    "score": 0.0,
                    "explanation": "No question ID specified",
                    "ai_used": False,
                }

            # Get user response - handle both flat and structured formats
            response = self._get_question_response(
                str(rule.question_id), form_responses
            )
            # loop through form_details.sections and get the question_id
            if form_details:
                for section in form_details.sections:  # type: ignore
                    for question in section.questions:  # type: ignore
                        if str(question.id) == str(rule.question_id):
                            question_meta = question
                            break
            # question_meta = form_details

            if response is None:
                return {
                    "score": 0.0,
                    "explanation": "Question not answered",
                    "ai_used": False,
                }

            # Score based on question type
            if question_meta.type in [
                QuestionType.SHORT_TEXT,
                QuestionType.LONG_TEXT,
            ]:
                return await self._score_text_question(rule, response, form_details)
            elif question_meta.type == QuestionType.NUMBER:
                return await self._score_numeric_question(rule, response)
            elif question_meta.type == QuestionType.RANGE:
                return await self._score_range_question(rule, response)
            elif question_meta.type == QuestionType.SINGLE_SELECT:
                return await self._score_single_select_question(rule, response)
            elif question_meta.type == QuestionType.MULTI_SELECT:
                return await self._score_multi_select_question(rule, response)
            elif question_meta.type == QuestionType.BOOLEAN:
                return await self._score_boolean_question(rule, response)
            elif question_meta.type == QuestionType.DATE:
                return await self._score_date_question(rule, response)
            elif question_meta.type == QuestionType.FILE:
                return {
                    "score": 0.0,
                    "explanation": "File questions are not scored",
                    "ai_used": False,
                }
            else:
                return {
                    "score": 0.0,
                    "explanation": f"Unsupported question type: {rule.question_type}",
                    "ai_used": False,
                }

        except Exception as e:
            logger.error(f"Error calculating rule score: {str(e)}", exc_info=True)
            return {"score": 0.0, "explanation": f"Error: {str(e)}", "ai_used": False}

    def _get_question_response(
        self, question_id: str, form_responses: Dict[str, Any]
    ) -> Any:
        """
        Get response for a question from form responses, handling both flat and structured formats.

        Form responses can be:
        1. Flat format: {question_id: answer, ...}
        2. Structured format: {answers: {question_id: answer}, repeatable_answers: {...}}
        3. Submission format: Direct answers dict
        """
        # Try direct lookup first (flat format or submission.answers)
        if question_id in form_responses:
            return form_responses[question_id]

        # Try structured format
        if "answers" in form_responses and isinstance(form_responses["answers"], dict):
            if question_id in form_responses["answers"]:
                return form_responses["answers"][question_id]

        # Try repeatable answers format
        if "repeatable_answers" in form_responses:
            repeatable_answers = form_responses["repeatable_answers"]
            if isinstance(repeatable_answers, dict):
                for section_id, instances in repeatable_answers.items():
                    if isinstance(instances, dict):
                        for instance_id, instance_answers in instances.items():
                            if (
                                isinstance(instance_answers, dict)
                                and question_id in instance_answers
                            ):
                                return instance_answers[question_id]

        return None

    def _get_section_responses(
        self, section_id: str, form_responses: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get all responses for a repeatable section.

        Returns a list of response dictionaries, one for each instance of the section.
        """
        section_responses = []

        # Check structured format first
        if "repeatable_answers" in form_responses:
            repeatable_answers = form_responses["repeatable_answers"]
            if (
                isinstance(repeatable_answers, dict)
                and section_id in repeatable_answers
            ):
                instances = repeatable_answers[section_id]
                if isinstance(instances, dict):
                    # Convert instances to list format
                    for instance_id, instance_answers in instances.items():
                        if isinstance(instance_answers, dict):
                            section_responses.append(instance_answers)

        # Fallback: look for section-based keys in flat format
        if not section_responses:
            section_key_prefix = f"section_{section_id}_"
            for key, value in form_responses.items():
                if key.startswith(section_key_prefix):
                    # This is a simplified approach - in practice, you'd need to group by instance
                    if isinstance(value, list):
                        section_responses.extend(value)
                    else:
                        section_responses.append({
                            key.replace(section_key_prefix, ""): value
                        })

        return section_responses

    async def _score_text_question(
        self, rule: ScoringRule, response: Any, form_details=None
    ) -> Dict[str, Any]:
        """Score text questions using AI evaluation."""
        try:
            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                condition_value = rule.condition.value
            else:
                # For CompoundCondition or other types, we can't extract value directly
                return {
                    "score": 0.0,
                    "explanation": "Text scoring requires FilterCondition with good/bad references",
                    "ai_used": False,
                }

            # Extract good/bad references from condition.value
            if not isinstance(condition_value, dict):
                return {
                    "score": 0.0,
                    "explanation": "Invalid text scoring configuration",
                    "ai_used": False,
                }

            good_reference = condition_value.get("good_reference", "")  # type: ignore
            bad_reference = condition_value.get("bad_reference", "")  # type: ignore

            if not good_reference or not bad_reference:
                return {
                    "score": 0.0,
                    "explanation": "Missing good/bad references",
                    "ai_used": False,
                }

            # Get question label from form details
            question_label = f"Question {rule.question_id}"
            if form_details and hasattr(form_details, "sections"):
                for section in form_details.sections:
                    for question in section.questions:
                        if str(question.id) == str(rule.question_id):
                            question_label = question.label
                            break

            # Use AI scoring service
            text_scoring_service = await self._get_text_scoring_service()
            ai_result = await text_scoring_service.score_text_response(
                question_label=question_label,
                user_answer=str(response),
                good_reference=good_reference,
                bad_reference=bad_reference,
            )

            return {
                "score": ai_result["score"],
                "explanation": ai_result["explanation"],
                "sources": ai_result.get("sources", []),
                "key_strengths": ai_result.get("key_strengths", []),
                "key_weaknesses": ai_result.get("key_weaknesses", []),
                "confidence": ai_result.get("confidence", 0.8),
                "ai_generated": ai_result.get("ai_generated", True),
                "ai_used": True,
            }

        except Exception as e:
            logger.error(f"Error in text scoring: {str(e)}", exc_info=True)
            return {
                "score": 0.0,
                "explanation": f"Text scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_numeric_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score numeric questions using operator-based comparison."""
        try:
            user_value = float(response)

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = float(rule.condition.value)
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Numeric scoring requires FilterCondition",
                    "ai_used": False,
                }

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_value == expected_value else 0.0
            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_value != expected_value else 0.0
            elif operator == ConditionOperator.GREATER_THAN:
                score = 1.0 if user_value > expected_value else 0.0
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                score = 1.0 if user_value >= expected_value else 0.0
            elif operator == ConditionOperator.LESS_THAN:
                score = 1.0 if user_value < expected_value else 0.0
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                score = 1.0 if user_value <= expected_value else 0.0
            else:
                score = 0.0

            explanation = f"User value {user_value} {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except (ValueError, TypeError) as e:
            return {
                "score": 0.0,
                "explanation": f"Invalid numeric value: {str(e)}",
                "ai_used": False,
            }

    async def _generate_founder_analysis(
        self, form_responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI analysis for founder signals (placeholder)."""
        # TODO: Implement comprehensive founder analysis
        return {
            "total_score": 85.0,
            "normalized_score": 85.0,
            "ai_analysis": "Founder analysis will be implemented in future iteration",
            "key_insights": [],
        }

    async def _generate_market_analysis(
        self, form_responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI analysis for market signals (placeholder)."""
        # TODO: Implement comprehensive market analysis
        return {
            "total_score": 80.0,
            "normalized_score": 80.0,
            "ai_analysis": "Market analysis will be implemented in future iteration",
            "key_insights": [],
        }

    async def _evaluate_condition(
        self,
        condition: Union[FilterCondition, CompoundCondition],
        form_responses: Dict[str, Any],
    ) -> bool:
        """Evaluate a condition against form responses."""
        try:
            if isinstance(condition, FilterCondition):
                # Get the response value
                response = form_responses.get(str(condition.question_id))
                if response is None:
                    return False

                # Evaluate based on operator
                if condition.operator is None:
                    return False
                return self._evaluate_operator(
                    condition.operator, response, condition.value
                )

            elif isinstance(condition, CompoundCondition):
                # Handle compound conditions using asyncio.gather to properly handle async operations
                if condition.operator == LogicalOperator.AND:
                    # Evaluate all conditions concurrently and check if all are True
                    results = await asyncio.gather(*[
                        self._evaluate_condition(c, form_responses)
                        for c in condition.conditions
                    ])
                    return all(results)
                elif condition.operator == LogicalOperator.OR:
                    # Evaluate all conditions concurrently and check if any is True
                    results = await asyncio.gather(*[
                        self._evaluate_condition(c, form_responses)
                        for c in condition.conditions
                    ])
                    return any(results)
                elif condition.operator == LogicalOperator.NOT:
                    # Evaluate the single condition and negate it
                    if condition.conditions:
                        result = await self._evaluate_condition(
                            condition.conditions[0], form_responses
                        )
                        return not result
                    return False

            return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}", exc_info=True)
            return False

    def _evaluate_operator(
        self, operator: ConditionOperator, actual_value: Any, expected_value: Any
    ) -> bool:
        """Evaluate an operator condition."""
        try:
            if operator == ConditionOperator.EQUALS:
                return actual_value == expected_value
            elif operator == ConditionOperator.NOT_EQUALS:
                return actual_value != expected_value
            elif operator == ConditionOperator.GREATER_THAN:
                return float(actual_value) > float(expected_value)
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                return float(actual_value) >= float(expected_value)
            elif operator == ConditionOperator.LESS_THAN:
                return float(actual_value) < float(expected_value)
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                return float(actual_value) <= float(expected_value)
            elif operator == ConditionOperator.CONTAINS:
                if isinstance(actual_value, list):
                    return any(
                        str(expected_value) in str(item) for item in actual_value
                    )
                return str(expected_value) in str(actual_value)
            elif operator == ConditionOperator.NOT_CONTAINS:
                if isinstance(actual_value, list):
                    return not any(
                        str(expected_value) in str(item) for item in actual_value
                    )
                return str(expected_value) not in str(actual_value)
            elif operator == ConditionOperator.IN:
                if isinstance(expected_value, list):
                    return actual_value in expected_value
                return actual_value == expected_value
            elif operator == ConditionOperator.NOT_IN:
                if isinstance(expected_value, list):
                    return actual_value not in expected_value
                return actual_value != expected_value
            elif operator == ConditionOperator.BETWEEN:
                if isinstance(expected_value, list) and len(expected_value) == 2:
                    return expected_value[0] <= float(actual_value) <= expected_value[1]
                return False
            elif operator == ConditionOperator.NOT_BETWEEN:
                if isinstance(expected_value, list) and len(expected_value) == 2:
                    return not (
                        expected_value[0] <= float(actual_value) <= expected_value[1]
                    )
                return False

            return False
        except (ValueError, TypeError) as e:
            logger.error(f"Error evaluating operator {operator}: {str(e)}")
            return False

    async def _calculate_aggregated_score(
        self, rule: ScoringRule, form_responses: Dict[str, Any], form_details=None
    ) -> Dict[str, Any]:
        """Calculate aggregated score for repeatable sections."""
        try:
            # Get all responses for this section using the proper method
            section_responses = self._get_section_responses(
                str(rule.section_id), form_responses
            )

            if not section_responses:
                return {
                    "score": 0.0,
                    "explanation": f"No responses found for repeatable section {rule.section_id}",
                    "aggregation_used": True,
                    "ai_used": False,
                }

            # Apply filter if present
            if rule.filter:
                filtered_responses = []
                for response in section_responses:
                    if await self._evaluate_condition(rule.filter, response):
                        filtered_responses.append(response)
                section_responses = filtered_responses

            if not section_responses:
                return {
                    "score": 0.0,
                    "explanation": "No responses passed the filter condition",
                    "aggregation_used": True,
                    "ai_used": False,
                }

            # Get individual scores for each response
            individual_scores = []
            for response in section_responses:
                if rule.value_field:
                    # Use specific field from response
                    value = response.get(str(rule.value_field))
                    if value is not None:
                        if rule.question_type in [
                            QuestionType.SHORT_TEXT,
                            QuestionType.LONG_TEXT,
                        ]:
                            score_result = await self._score_text_question_value(
                                rule, value
                            )
                            individual_scores.append(score_result["score"])
                        else:
                            # Handle FilterCondition for aggregated scoring
                            if (
                                isinstance(rule.condition, FilterCondition)
                                and rule.condition.operator
                            ):
                                score = self._evaluate_operator(
                                    rule.condition.operator, value, rule.condition.value
                                )
                                individual_scores.append(1.0 if score else 0.0)
                            else:
                                individual_scores.append(0.0)
                else:
                    # Use entire response
                    if rule.question_type in [
                        QuestionType.SHORT_TEXT,
                        QuestionType.LONG_TEXT,
                    ]:
                        score_result = await self._score_text_question_value(
                            rule, response
                        )
                        individual_scores.append(score_result["score"])
                    else:
                        score = await self._evaluate_condition(rule.condition, response)
                        individual_scores.append(1.0 if score else 0.0)

            if not individual_scores:
                return {
                    "score": 0.0,
                    "explanation": "No valid scores calculated",
                    "aggregation_used": True,
                    "ai_used": False,
                }

            # Apply aggregation
            final_score = 0.0
            explanation = ""

            if rule.aggregation == AggregationType.ANY:
                final_score = 1.0 if any(s > 0 for s in individual_scores) else 0.0
                explanation = f"ANY aggregation: {sum(1 for s in individual_scores if s > 0)}/{len(individual_scores)} instances matched"

            elif rule.aggregation == AggregationType.ALL:
                final_score = 1.0 if all(s > 0 for s in individual_scores) else 0.0
                explanation = f"ALL aggregation: {'All' if final_score > 0 else 'Not all'} {len(individual_scores)} instances matched"

            elif rule.aggregation == AggregationType.COUNT:
                matching_count = sum(1 for s in individual_scores if s > 0)
                final_score = matching_count / len(individual_scores)
                explanation = f"COUNT aggregation: {matching_count}/{len(individual_scores)} instances matched"

            elif rule.aggregation == AggregationType.PERCENTAGE:
                matching_count = sum(1 for s in individual_scores if s > 0)
                percentage = (matching_count / len(individual_scores)) * 100
                threshold = rule.aggregate_threshold or 50.0
                final_score = 1.0 if percentage >= threshold else 0.0
                explanation = f"PERCENTAGE aggregation: {percentage:.1f}% matched (threshold: {threshold}%)"

            elif rule.aggregation == AggregationType.AVG:
                final_score = sum(individual_scores) / len(individual_scores)
                explanation = f"AVG aggregation: Average score {final_score:.2f} from {len(individual_scores)} instances"

            elif rule.aggregation == AggregationType.SUM:
                final_score = sum(individual_scores)
                explanation = f"SUM aggregation: Total score {final_score:.2f} from {len(individual_scores)} instances"

            elif rule.aggregation == AggregationType.MIN:
                final_score = min(individual_scores)
                explanation = f"MIN aggregation: Minimum score {final_score:.2f} from {len(individual_scores)} instances"

            elif rule.aggregation == AggregationType.MAX:
                final_score = max(individual_scores)
                explanation = f"MAX aggregation: Maximum score {final_score:.2f} from {len(individual_scores)} instances"

            else:
                final_score = sum(individual_scores) / len(individual_scores)
                explanation = f"Default AVG aggregation: {final_score:.2f} from {len(individual_scores)} instances"

            return {
                "score": final_score,
                "explanation": explanation,
                "aggregation_used": True,
                "aggregation_type": rule.aggregation.value
                if rule.aggregation
                else "none",
                "individual_scores": individual_scores,
                "ai_used": False,
            }

        except Exception as e:
            logger.error(f"Error in aggregated scoring: {str(e)}", exc_info=True)
            return {
                "score": 0.0,
                "explanation": f"Aggregation error: {str(e)}",
                "aggregation_used": True,
                "ai_used": False,
            }

    async def _score_text_question_value(
        self, rule: ScoringRule, value: Any
    ) -> Dict[str, Any]:
        """Score a single text value using the same logic as _score_text_question."""
        try:
            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                condition_value = rule.condition.value
            else:
                return {
                    "score": 0.0,
                    "explanation": "Text scoring requires FilterCondition with good/bad references",
                    "ai_used": False,
                }

            if not isinstance(condition_value, dict):
                return {
                    "score": 0.0,
                    "explanation": "Invalid text scoring configuration",
                }

            good_reference = condition_value.get("good_reference", "")  # type: ignore
            bad_reference = condition_value.get("bad_reference", "")  # type: ignore

            if not good_reference or not bad_reference:
                return {"score": 0.0, "explanation": "Missing good/bad references"}

            # Use AI scoring service
            text_scoring_service = await self._get_text_scoring_service()
            ai_result = await text_scoring_service.score_text_response(
                question_label=f"Question {rule.question_id}",
                user_answer=str(value),
                good_reference=good_reference,
                bad_reference=bad_reference,
            )

            return {
                "score": ai_result["score"],
                "explanation": ai_result["explanation"],
                "ai_generated": ai_result.get("ai_generated", True),
            }

        except Exception as e:
            logger.error(f"Error in text value scoring: {str(e)}", exc_info=True)
            return {"score": 0.0, "explanation": f"Text scoring error: {str(e)}"}

    async def _score_range_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score range questions using operator-based comparison."""
        try:
            # Range responses are typically [min, max] arrays
            if isinstance(response, list) and len(response) == 2:
                user_min, user_max = float(response[0]), float(response[1])
                user_value = (user_min + user_max) / 2  # Use midpoint for comparison
            else:
                user_value = float(response)  # type: ignore

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Range scoring requires FilterCondition",
                    "ai_used": False,
                }

            # Use same logic as numeric questions
            if operator == ConditionOperator.EQUALS:
                score = 1.0 if abs(user_value - float(expected_value)) < 0.01 else 0.0
            elif operator == ConditionOperator.GREATER_THAN:
                score = 1.0 if user_value > float(expected_value) else 0.0
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                score = 1.0 if user_value >= float(expected_value) else 0.0
            elif operator == ConditionOperator.LESS_THAN:
                score = 1.0 if user_value < float(expected_value) else 0.0
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                score = 1.0 if user_value <= float(expected_value) else 0.0
            elif operator == ConditionOperator.BETWEEN:
                if isinstance(expected_value, list) and len(expected_value) == 2:
                    min_val, max_val = expected_value
                    score = 1.0 if min_val <= user_value <= max_val else 0.0
                else:
                    score = 0.0
            else:
                score = 0.0

            explanation = f"Range value {user_value} {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except (ValueError, TypeError) as e:
            return {
                "score": 0.0,
                "explanation": f"Invalid range value: {str(e)}",
                "ai_used": False,
            }

    async def _score_single_select_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score single select questions using operator-based comparison."""
        try:
            user_value = str(response)

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Single select scoring requires FilterCondition",
                    "ai_used": False,
                }

            # if operator == ConditionOperator.EQUALS:
            #     score = 1.0 if user_value == str(expected_value) else 0.0
            # elif operator == ConditionOperator.NOT_EQUALS:
            #     score = 1.0 if user_value != str(expected_value) else 0.0
            # elif operator == ConditionOperator.IN:
            #     if isinstance(expected_value, list):
            #         score = (
            #             1.0 if user_value in [str(v) for v in expected_value] else 0.0
            #         )
            #     else:
            #         score = 1.0 if user_value == str(expected_value) else 0.0
            # elif operator == ConditionOperator.NOT_IN:
            #     if isinstance(expected_value, list):
            #         score = (
            #             1.0
            #             if user_value not in [str(v) for v in expected_value]
            #             else 0.0
            #         )
            #     else:
            #         score = 1.0 if user_value != str(expected_value) else 0.0
            # else:
            #     score = 0.0
            expected_values = (
                [str(expected_value)]
                if not isinstance(expected_value, list)
                else [str(v) for v in expected_value]
            )

            user_value_str = str(user_value)

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_value_str in expected_values else 0.0

            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_value_str not in expected_values else 0.0

            elif operator == ConditionOperator.IN:
                score = 1.0 if user_value_str in expected_values else 0.0

            elif operator == ConditionOperator.NOT_IN:
                score = 1.0 if user_value_str not in expected_values else 0.0

            else:
                score = 0.0

            explanation = f"Selected '{user_value}' {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Single select scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_multi_select_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score multi select questions with proportional scoring."""
        try:
            user_values = response if isinstance(response, list) else [response]
            user_values = [str(v) for v in user_values]

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_values = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Multi select scoring requires FilterCondition",
                    "ai_used": False,
                }

            if not isinstance(expected_values, list):
                expected_values = [expected_values]
            expected_values = [str(v) for v in expected_values]

            if operator == ConditionOperator.CONTAINS:
                # Proportional credit for overlap
                overlap = len(set(user_values).intersection(set(expected_values)))
                score = overlap / len(expected_values) if expected_values else 0.0
                explanation = (
                    f"Selected {overlap}/{len(expected_values)} expected options"
                )
            elif operator == ConditionOperator.IN:
                # Any one expected option present
                score = 1.0 if any(v in expected_values for v in user_values) else 0.0
                explanation = f"At least one expected option selected: {'✓' if score > 0 else '✗'}"
            elif operator == ConditionOperator.NOT_IN:
                # None of the expected options present
                score = (
                    1.0 if not any(v in expected_values for v in user_values) else 0.0
                )
                explanation = (
                    f"No forbidden options selected: {'✓' if score > 0 else '✗'}"
                )
            else:
                # Default to contains behavior
                overlap = len(set(user_values).intersection(set(expected_values)))
                score = overlap / len(expected_values) if expected_values else 0.0
                explanation = f"Selected {overlap}/{len(expected_values)} expected options (default)"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Multi select scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_boolean_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score boolean questions using exact match."""
        try:
            user_value = bool(response)

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = bool(rule.condition.value)
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Boolean scoring requires FilterCondition",
                    "ai_used": False,
                }

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_value == expected_value else 0.0
            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_value != expected_value else 0.0
            else:
                score = 0.0

            explanation = f"Boolean {user_value} {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Boolean scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_date_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score date questions using date comparison."""
        try:
            from datetime import datetime

            # Parse user date
            if isinstance(response, str):
                user_date = datetime.fromisoformat(response.replace("Z", "+00:00"))
            elif isinstance(response, (int, float)):
                user_date = datetime.fromtimestamp(response)
            else:
                user_date = response

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Date scoring requires FilterCondition",
                    "ai_used": False,
                }

            # Parse expected date
            if isinstance(expected_value, str):
                expected_date = datetime.fromisoformat(
                    expected_value.replace("Z", "+00:00")
                )
            elif isinstance(expected_value, (int, float)):
                expected_date = datetime.fromtimestamp(expected_value)
            else:
                expected_date = expected_value

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_date.date() == expected_date.date() else 0.0
            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_date.date() != expected_date.date() else 0.0
            elif operator == ConditionOperator.GREATER_THAN:
                score = 1.0 if user_date > expected_date else 0.0
            elif operator == ConditionOperator.LESS_THAN:
                score = 1.0 if user_date < expected_date else 0.0
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                score = 1.0 if user_date >= expected_date else 0.0
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                score = 1.0 if user_date <= expected_date else 0.0
            else:
                score = 0.0

            explanation = f"Date {user_date.date()} {operator.value if operator else 'unknown'} expected {expected_date.date()}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Date scoring error: {str(e)}",
                "ai_used": False,
            }
