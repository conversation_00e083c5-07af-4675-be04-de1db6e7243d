import re
from datetime import datetime
from typing import Optional, Dict, Any, List
from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.core.logging import get_logger
from app.core.security import get_password_hash
from app.models.user import InviteCode, InviteCodeStatus, User, UserStatus
from app.models.organization import Organization, OrganizationSettings
from app.models.role import Role
from app.services.onboarding.interface import IOnboardingService
from app.services.auth.interface import IAuthService
from app.services.email.interface import IEmailService
from app.utils.common import PyObjectId

logger = get_logger(__name__)


class OnboardingService(IOnboardingService):
    """Service for handling organization onboarding workflows."""

    def __init__(self, auth_service: IAuthService, email_service: IEmailService):
        self.auth_service = auth_service
        self.email_service = email_service

    async def create_invite_code(
        self, 
        email: str, 
        org_name: Optional[str] = None
    ) -> InviteCode:
        """Create a new invite code for organization onboarding."""
        try:
            # Check if email already has a pending invite code
            existing_code = await InviteCode.find_one({
                "email": email,
                "status": InviteCodeStatus.PENDING
            })
            
            if existing_code and not existing_code.is_expired():
                logger.info(f"Returning existing invite code for {email}")
                return existing_code

            # Generate unique code
            max_attempts = 10
            for _ in range(max_attempts):
                code = InviteCode.generate_code()
                existing = await InviteCode.find_one({"code": code})
                if not existing:
                    break
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to generate unique invite code"
                )

            # Create new invite code
            invite_code = InviteCode(
                code=code,
                email=email,
                org_name=org_name
            )
            await invite_code.save()

            logger.info(f"Created invite code {code} for {email}")
            return invite_code

        except Exception as e:
            logger.error(f"Error creating invite code: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create invite code"
            )

    async def validate_invite_code(self, code: str) -> InviteCode:
        """Validate and retrieve an invite code."""
        invite_code = await InviteCode.find_one({"code": code})
        
        if not invite_code:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invite code not found"
            )

        if not invite_code.is_redeemable():
            if invite_code.is_expired():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invite code has expired"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invite code has already been used"
                )

        return invite_code

    async def check_subdomain_availability(self, subdomain: str) -> bool:
        """Check if a subdomain is available for use."""
        # Validate subdomain format
        if not re.match(r'^[a-z0-9-]+$', subdomain):
            return False
        
        if len(subdomain) < 3 or len(subdomain) > 50:
            return False

        # Check if subdomain already exists
        existing_org = await Organization.find_one({"subdomain": subdomain})
        return existing_org is None

    async def create_organization(
        self,
        invite_code: InviteCode,
        org_name: str,
        subdomain: str,
        website: Optional[str] = None,
        logo_url: Optional[str] = None
    ) -> Organization:
        """Create a new organization during onboarding."""
        try:
            # Validate subdomain availability
            if not await self.check_subdomain_availability(subdomain):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Subdomain is not available"
                )

            # Create organization
            organization = Organization(
                name=org_name,
                subdomain=subdomain,
                website=website,
                logo_url=logo_url,
                contact_email=invite_code.email,
                created_by=PyObjectId(),  # Will be updated after user creation
                settings=OrganizationSettings(
                    plan="basic",
                    subdomain_enabled=True,
                    features={"onboarding_completed": True}
                )
            )
            await organization.save()

            logger.info(f"Created organization {org_name} with subdomain {subdomain}")
            return organization

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating organization: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create organization"
            )

    async def create_user_profile(
        self,
        invite_code: InviteCode,
        organization: Organization,
        name: str,
        password: str,
        profile_picture_url: Optional[str] = None
    ) -> User:
        """Create user profile and complete onboarding."""
        try:
            # Check if user already exists
            existing_user = await User.find_one({"email": invite_code.email})
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists"
                )

            # Get default role (admin for first user)
            admin_role = await Role.find_one({"name": "admin"})
            if not admin_role:
                # Create default admin role if it doesn't exist
                admin_role = Role(
                    name="admin",
                    description="Organization administrator",
                    permissions=["*"]  # Full permissions
                )
                await admin_role.save()

            # Create user
            user = User(
                name=name,
                email=invite_code.email,
                password_hash=get_password_hash(password),
                org_id=organization.id,
                org_ids=[organization.id],
                role_id=admin_role.id,
                status=UserStatus.ACTIVE,
                is_active=True
            )
            await user.save()

            # Update organization with creator info
            organization.created_by = user.id
            organization.admins = [user.id]
            organization.members = [user.id]
            organization.user_ids = [str(user.id)]
            await organization.save(is_update=True)

            # Mark invite code as redeemed
            invite_code.redeem(user.id)
            await invite_code.save(is_update=True)

            logger.info(f"Created user profile for {invite_code.email}")
            return user

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating user profile: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user profile"
            )

    async def complete_onboarding(
        self,
        invite_code: InviteCode,
        user: User,
        organization: Organization
    ) -> Dict[str, Any]:
        """Complete the onboarding process and return auth tokens."""
        try:
            # Generate auth tokens
            token_response = await self.auth_service.create_tokens(user)

            return {
                "user_id": str(user.id),
                "org_id": str(organization.id),
                "access_token": token_response.access_token,
                "refresh_token": token_response.refresh_token,
                "redirect_url": "/dashboard"
            }

        except Exception as e:
            logger.error(f"Error completing onboarding: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to complete onboarding"
            )

    async def send_peer_invites(
        self,
        org_id: str,
        invited_by: str,
        emails: List[str],
        role_id: Optional[str] = None,
        message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Send invitations to peers to join the organization."""
        try:
            results = []
            
            for email in emails:
                try:
                    # Use existing invite functionality
                    result = await self.auth_service.invite_user(
                        name="",  # Will be filled during acceptance
                        email=email,
                        role_id=role_id or "",
                        org_id=org_id,
                        invited_by=invited_by
                    )
                    results.append({"email": email, "status": "sent", "result": result})
                except Exception as e:
                    logger.error(f"Failed to invite {email}: {str(e)}")
                    results.append({"email": email, "status": "failed", "error": str(e)})

            return {
                "total_sent": len([r for r in results if r["status"] == "sent"]),
                "total_failed": len([r for r in results if r["status"] == "failed"]),
                "results": results
            }

        except Exception as e:
            logger.error(f"Error sending peer invites: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send peer invites"
            )
