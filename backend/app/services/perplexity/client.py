"""
Perplexity API Client

Handles async chat completions with Perplexity Sonar API.
"""

from typing import Any, Dict, List, Optional

import httpx

from app.core.config import settings
from app.core.logging import get_logger
from app.models.chat import ChatSource

logger = get_logger(__name__)


class PerplexityError(Exception):
    """Base exception for Perplexity API errors."""

    pass


class PerplexityRateLimitError(PerplexityError):
    """Raised when rate limit is exceeded."""

    pass


class PerplexityTimeoutError(PerplexityError):
    """Raised when request times out."""

    pass


class PerplexityClient:
    """
    Async client for Perplexity Sonar API.
    Handles chat completions with sources and citations.
    """

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or getattr(settings, "PERPLEXITY_API_KEY", None)
        if not self.api_key:
            raise ValueError("Perplexity API key is required")

        self.base_url = "https://api.perplexity.ai"
        self.timeout = 30.0

        # Default headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "sonar-reasoning",
        max_tokens: int = 1000,
        temperature: float = 0.2,
    ) -> Dict[str, Any]:
        """
        Create a synchronous chat completion request.

        Returns:
            completion_data: Full completion response with content and citations
        """
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": 0.9,
            "return_images": False,
            "return_related_questions": False,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 0,
        }

        try:
            async with httpx.AsyncClient(
                timeout=60.0
            ) as client:  # Increased timeout for sync calls
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload,
                )

                if response.status_code == 429:
                    raise PerplexityRateLimitError(
                        "Rate limit exceeded. Please try again later."
                    )
                elif response.status_code == 400:
                    error_detail = response.text
                    raise PerplexityError(f"Bad request: {error_detail}")
                elif response.status_code == 401:
                    raise PerplexityError("Invalid API key")
                elif response.status_code == 403:
                    raise PerplexityError(
                        "Access forbidden. Check your API permissions."
                    )
                elif response.status_code >= 500:
                    raise PerplexityError(
                        f"Perplexity server error ({response.status_code}). Please try again."
                    )
                elif response.status_code >= 400:
                    error_detail = response.text
                    raise PerplexityError(
                        f"API error {response.status_code}: {error_detail}"
                    )

                result = response.json()

                if not result.get("choices"):
                    raise PerplexityError(
                        "No response choices returned from Perplexity API"
                    )

                logger.info(f"Completed Perplexity chat completion with model: {model}")
                return result

        except httpx.TimeoutException:
            raise PerplexityTimeoutError("Request timed out. Please try again.")
        except httpx.RequestError as e:
            logger.error(f"Network error during completion: {e}")
            raise PerplexityError(
                "Network error. Please check your connection and try again."
            )
        except Exception as e:
            logger.error(f"Error creating chat completion: {e}")
            if isinstance(
                e, (PerplexityError, PerplexityRateLimitError, PerplexityTimeoutError)
            ):
                raise
            raise PerplexityError(f"Failed to create completion: {str(e)}")

    def extract_sources_from_completion(
        self, completion_data: Dict[str, Any]
    ) -> List[ChatSource]:
        """
        Extract sources/citations from Perplexity completion response.
        """
        sources = []

        try:
            # For synchronous completions, citations are in the response
            citations = completion_data.get("citations", [])

            # Also check in choices if citations are nested there
            if not citations and completion_data.get("choices"):
                choice = completion_data["choices"][0]
                citations = choice.get("citations", [])

            for citation in citations:
                source = ChatSource(
                    title=citation.get("title", "Unknown Source"),
                    url=citation.get("url", ""),
                    snippet=citation.get("snippet"),
                    domain=citation.get("domain"),
                )
                sources.append(source)

        except Exception as e:
            logger.error(f"Error extracting sources: {e}")

        return sources

    def extract_content_from_completion(self, completion_data: Dict[str, Any]) -> str:
        """
        Extract content from Perplexity completion response.
        """
        try:
            # For synchronous completions, content is in choices
            choices = completion_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                return content

            # Fallback to direct content field
            return completion_data.get("content", "No response generated")

        except Exception as e:
            logger.error(f"Error extracting content from completion: {e}")
            return "Error processing response"

    def build_investment_prompt(
        self,
        user_message: str,
        deal_context: Optional[Dict[str, Any]] = None,
        chat_history: Optional[List[Dict[str, str]]] = None,
        mode: str = "research",
    ) -> List[Dict[str, str]]:
        """
        Build a context-aware prompt for investment research analysis.
        """
        messages = []

        # System message for Deep Research mode - analyst-grade, sourced
        system_content = """You are Orbit, TractionX's AI research analyst. You provide analyst-grade, fact-checked investment research with comprehensive sources and citations.

Key guidelines for Deep Research mode:
- ALWAYS provide credible sources and citations for every claim
- Focus on data-driven, investment-relevant insights
- Structure responses with clear sections and bullet points
- Include market data, competitive analysis, and financial metrics when available
- Cite recent news, funding rounds, and industry reports
- Be thorough and comprehensive in your analysis
- If information is limited, clearly state this and suggest what additional research is needed
- Format responses for professional investment analysis"""

        # Add deal context if provided
        if deal_context:
            company_name = deal_context.get("company_name", "this company")
            sector = deal_context.get("sector", "")
            stage = deal_context.get("stage", "")

            system_content += "\n\nCurrent deal context:\n"
            system_content += f"- Company: {company_name}\n"
            if sector:
                system_content += f"- Sector: {sector}\n"
            if stage:
                system_content += f"- Stage: {stage}\n"

            system_content += (
                "\nProvide research specifically relevant to this deal and context."
            )

        messages.append({"role": "system", "content": system_content})

        # Add chat history if provided (last 6 messages for research context)
        if chat_history:
            messages.extend(chat_history[-6:])

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        return messages
