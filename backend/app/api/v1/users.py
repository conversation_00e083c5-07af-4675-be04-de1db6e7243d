from bson import ObjectId
from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase
from pydantic import BaseModel

from app.api.base import BaseAPIRouter
from app.core import logging
from app.core.database import get_database
from app.dependencies.auth import get_current_user
from app.models.audit import AuditLog
from app.models.user import User
from app.services.audit.interfaces import AuditServiceInterface
from app.services.auth.interface import IAuthService
from app.services.factory import (
    get_audit_service,
    get_auth_service,
    get_rbac_service,
    get_user_service,
)
from app.services.rbac.interfaces import RBACServiceInterface
from app.services.user.interfaces import UserServiceInterface
from app.services.user.mongo import UserService
from app.utils.rbac.rbac import rbac_register

logger = logging.get_logger(__name__)
router = BaseAPIRouter(prefix="/users", tags=["users"])


class SuperUserCreate(BaseModel):
    email: str
    password: str
    name: str


@router.post("/create-superuser")
@rbac_register(resource="users", action="create", hidden=True)
async def create_superuser(
    user_data: SuperUserCreate,
    db: AsyncIOMotorDatabase = Depends(get_database),
    auth_service: IAuthService = Depends(get_auth_service),
    audit_service: AuditServiceInterface = Depends(get_audit_service),
):
    """Create the first superuser account. This endpoint can only be used if no superusers exist."""
    # Check if any superuser exists
    existing_superuser = await db.users.find_one({"is_superuser": True})
    if existing_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="A superuser already exists. This endpoint can only be used to create the first superuser.",
        )

    # Check if user with this email exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists",
        )

    # Hash password
    password_hash = auth_service.get_password_hash(user_data.password)  # type: ignore

    user = User(
        email=user_data.email,
        password_hash=password_hash,
        name=user_data.name,
        is_superuser=True,
        status="active",
    )  # type: ignore
    result = await db.users.insert_one(user.dict(by_alias=True))
    user.id = result.inserted_id

    # log action
    audit_log = AuditLog(
        user_id=user.id,
        action="create_superuser",
        entity_type="user",
        entity_id=user.id,
    )
    await audit_service.log_action(audit_log)

    return {"message": "Superuser created successfully", "user_id": str(user.id)}


@router.put("/{user_id}/role")
@rbac_register(
    resource="users", action="edit", group="Users", description="Update user role"
)
async def update_user_role(
    user_id: str,
    role_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Update user's role."""
    # Check if user has permission to update roles
    user_service = UserService(db)
    has_permission = await user_service.check_permission(
        current_user.role_id, "users", "edit", current_user.org_id
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
        )

    # Update role
    result = await user_service.update_user(
        {"_id": ObjectId(user_id), "org_id": current_user.org_id},
        {"$set": {"role_id": ObjectId(role_id)}},
    )

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Log role update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_role",
        entity_type="user",
        entity_id=ObjectId(user_id),
        metadata={"new_role_id": role_id},
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    return {"message": "Role updated successfully"}


@router.put("/{user_id}/status")
@rbac_register(
    resource="users", action="edit", group="Users", description="Update user status"
)
async def update_user_status(
    user_id: str,
    status: str,
    current_user: User = Depends(get_current_user),
    rbac_service: RBACServiceInterface = Depends(get_rbac_service),
    user_service: UserServiceInterface = Depends(get_user_service),
    audit_service: AuditServiceInterface = Depends(get_audit_service),
):
    """Update user's status (active/suspended)."""
    # Check permissions
    has_permission = await rbac_service.check_permission(
        current_user.id, "users", "edit"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
        )

    if status not in ["active", "suspended"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid status"
        )

    # Update status
    result = await user_service.update_user(
        {"_id": ObjectId(user_id)}, {"$set": {"status": status}}
    )

    if result.modified_count == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Log status update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_status",
        entity_type="user",
        entity_id=ObjectId(user_id),
        metadata={"new_status": status},
    )
    # log action
    await audit_service.log_audit(audit_log)

    return {"message": "Status updated successfully"}


@router.get("/me")
@rbac_register(resource="users", action="view", hidden=True)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Get current user's information."""
    return {
        "user_id": str(current_user.id),
        "email": current_user.email,
        "name": current_user.name,
        "org_id": str(current_user.org_id) if current_user.org_id else None,
        "role_id": str(current_user.role_id) if current_user.role_id else None,
        "status": current_user.status,
    }


# TODO: Implement user update endpoint
# @router.put("")
# @rbac_register(resource="users", action="edit", hidden=True)
# async def update_user(
#     user_data: UserUpdate,
#     current_user: User = Depends(get_current_user),
# ):
#     """Update user."""
#     return {"message": "User updated successfully"}


@router.get("/all")
@rbac_register(resource="users", action="view", hidden=True)
async def get_all_users(
    current_user: User = Depends(get_current_user),
):
    """Get all users."""
    org_id = current_user.org_id
    users = await User.find_many({"org_id": org_id})
    # remove password_hash from the response
    return [user.model_dump(by_alias=True, exclude={"password_hash"}) for user in users]
