"""
Upload endpoints for profile pictures, organization logos, and other assets.
"""

from typing import Optional
import uuid
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.file.service import FileService

logger = get_logger(__name__)

router = APIRouter(prefix="/uploads", tags=["Uploads"])


class GenerateUploadUrlRequest(BaseModel):
    """Request model for generating presigned upload URL for assets."""
    
    file_type: str = Field(..., description="Type of file (avatar, logo)")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")


class GenerateUploadUrlResponse(BaseModel):
    """Response model for presigned upload URL."""
    
    presigned_url: str = Field(..., description="Presigned URL for upload")
    public_url: str = Field(..., description="Public URL for accessing the file")
    s3_key: str = Field(..., description="S3 key for the uploaded file")
    expires_in: int = Field(..., description="URL expiration time in seconds")


@router.post("/generate-presigned-url", response_model=GenerateUploadUrlResponse)
async def generate_presigned_upload_url(
    request: GenerateUploadUrlRequest,
    current_user: User = Depends(get_current_user)
) -> GenerateUploadUrlResponse:
    """
    Generate a presigned URL for uploading profile pictures, logos, etc.
    
    This endpoint creates presigned URLs for uploading user avatars and organization logos
    directly to S3. The files are stored in a public bucket with unguessable paths.
    """
    try:
        # Validate file type
        allowed_types = ["avatar", "logo"]
        if request.file_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed types: {allowed_types}"
            )
        
        # Validate content type
        allowed_content_types = [
            "image/jpeg", "image/png", "image/webp", "image/svg+xml"
        ]
        if request.content_type not in allowed_content_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid content type. Allowed types: {allowed_content_types}"
            )
        
        # Validate file size (10MB max for logos, 5MB for avatars)
        max_size = 10 * 1024 * 1024 if request.file_type == "logo" else 5 * 1024 * 1024
        if request.file_size > max_size:
            max_mb = max_size // (1024 * 1024)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size: {max_mb}MB"
            )
        
        # Generate unique S3 key
        file_extension = request.filename.split('.')[-1].lower()
        unique_id = str(uuid.uuid4())
        s3_key = f"assets/{request.file_type}s/{current_user.org_id}/{unique_id}.{file_extension}"
        
        # Initialize file service
        file_service = FileService()
        await file_service.initialize()
        
        # Generate presigned URL for PUT operation
        presigned_url = file_service.s3_client.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': settings.S3_BUCKET_ASSETS,  # Use assets bucket
                'Key': s3_key,
                'ContentType': request.content_type,
                'ContentLength': request.file_size,
                'ACL': 'public-read'  # Make files publicly readable
            },
            ExpiresIn=settings.S3_PRESIGNED_URL_EXPIRY
        )
        
        # Generate public URL for accessing the file
        public_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{s3_key}"
        
        logger.info(f"Generated presigned upload URL for {request.file_type} by user {current_user.email}")
        
        return GenerateUploadUrlResponse(
            presigned_url=presigned_url,
            public_url=public_url,
            s3_key=s3_key,
            expires_in=settings.S3_PRESIGNED_URL_EXPIRY
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating presigned upload URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate upload URL"
        )


@router.delete("/asset/{s3_key:path}")
async def delete_asset(
    s3_key: str,
    current_user: User = Depends(get_current_user)
) -> dict:
    """
    Delete an uploaded asset (avatar, logo, etc.) from S3.
    
    This endpoint allows users to delete their uploaded assets. It validates
    that the user has permission to delete the asset based on the S3 key path.
    """
    try:
        # Validate that the user can delete this asset
        # S3 key format: assets/{type}s/{org_id}/{unique_id}.{ext}
        key_parts = s3_key.split('/')
        if len(key_parts) < 3 or key_parts[0] != "assets":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid asset key"
            )
        
        asset_org_id = key_parts[2]
        if asset_org_id != str(current_user.org_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to delete this asset"
            )
        
        # Initialize file service and delete from S3
        file_service = FileService()
        await file_service.initialize()
        
        file_service.s3_client.delete_object(
            Bucket=settings.S3_BUCKET_ASSETS,
            Key=s3_key
        )
        
        logger.info(f"Deleted asset {s3_key} by user {current_user.email}")
        
        return {"message": "Asset deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting asset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete asset"
        )
