from datetime import datetime
from typing import Dict, List, Optional

from bson import ObjectId
from pydantic import Field, validator

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId


class OrganizationSettings(TractionXModel):
    """
    Stores settings specific to an organization, such as plan, branding, and feature flags.
    """

    plan: str = Field(
        default="basic", description="Organization plan (basic, advanced, enterprise)"
    )
    subdomain_enabled: bool = Field(
        default=True, description="Whether subdomain access is enabled"
    )
    features: Dict[str, bool] = Field(default_factory=dict, description="Feature flags")
    custom_domain: Optional[str] = Field(
        None, description="Custom domain for the organization"
    )
    branding: Dict[str, str] = Field(
        default_factory=dict, description="Branding settings"
    )
    pass_through: bool = Field(
        default=False,
        description="If True, disables all auth, RBAC, and tenant checks for this org",
    )


class Organization(TractionXModel):
    """
    Represents an organization, with metadata, members, admins, and settings.
    """

    id: ObjectIdField = Field(default_factory=ObjectId, alias="_id")
    name: str
    subdomain: str = Field(..., description="Unique subdomain for organization")
    description: Optional[str] = None
    website: Optional[str] = None
    logo_url: Optional[str] = None
    address: Optional[str] = None
    contact_email: Optional[str] = None
    created_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    is_active: bool = True
    members: List[ObjectIdField] = []
    admins: List[ObjectIdField] = []
    user_ids: List[str] = Field(
        default_factory=list,
        description="List of user IDs with access to this organization",
    )  # type: ignore
    settings: OrganizationSettings = Field(
        default_factory=OrganizationSettings,
        description="Organization-specific settings",
    )
    contact_phone: Optional[str] = None
    created_by: ObjectIdField = Field(default_factory=PyObjectId, alias="created_by")
    created_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    is_active: bool = True
    user_ids: List[ObjectIdField] = Field(
        default_factory=ObjectId,
        description="List of user IDs with access to this organization",
    )  # type: ignore
    settings: OrganizationSettings = Field(
        default_factory=OrganizationSettings,  # type: ignore
        description="Organization-specific settings",
    )

    @validator("subdomain")
    def validate_subdomain(cls, v):
        if not v.isalnum():
            raise ValueError("Subdomain must be alphanumeric")
        return v.lower()

    @validator("settings")
    def validate_settings(cls, v):
        if v.plan not in ["basic", "advanced", "enterprise"]:
            raise ValueError("Invalid plan type")
        return v
