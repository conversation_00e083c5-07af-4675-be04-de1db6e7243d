import json
import time
from typing import Any, Callable

from bson import ObjectId
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse
from jose import jwt  # type: ignore
from starlette.middleware.base import BaseHTTPMiddleware

from app.api.v1 import protected_api_router, public_api_router
from app.core.auth_exceptions import AuthError
from app.core.cache import cache
from app.core.config import settings
from app.core.database import db
from app.core.logging import configure_logging, get_logger
from app.middleware.auth import AuthMiddleware
from app.middleware.org_context import OrgContextMiddleware
from app.services.factory import ServiceFactory, get_auth_service, get_rbac_service


# Custom JSON encoder for MongoDB ObjectId
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj: Any) -> Any:
        if isinstance(obj, ObjectId):
            return str(obj)
        return super().default(obj)


# Custom JSONResponse that uses our encoder
class CustomJSONResponse(JSONResponse):
    def render(self, content: Any) -> bytes:
        return json.dumps(
            content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
            cls=CustomJSONEncoder,
        ).encode("utf-8")


logger = get_logger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        logger.info(
            "request_processed",
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            process_time=process_time,
        )
        return response


def create_application() -> FastAPI:
    """Create FastAPI application."""
    configure_logging()

    application = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description=settings.DESCRIPTION,
        docs_url=None,  # Disable default docs
        redoc_url=None,  # Disable default redoc
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        default_response_class=CustomJSONResponse,  # Use our custom JSONResponse
    )

    # Set FastAPI app in service factory
    ServiceFactory.set_app(application)

    # IMPORTANT: CORS middleware must be added BEFORE auth middleware
    # Add CORS middleware with explicit configuration for development
    application.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:8080",
            "http://localhost:3000",
        ],  # Specify your frontend origins
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],  # Allow all headers for simplicity in development
        expose_headers=["*"],  # Expose all headers
        max_age=600,  # Cache preflight requests for 10 minutes
    )

    # Log CORS configuration
    logger.info(
        f"CORS configuration: origins={['http://localhost:8080', 'http://localhost:3000']}, methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH']"
    )

    # Add trusted host middleware
    # application.add_middleware(
    #     TrustedHostMiddleware,
    #     allowed_hosts=settings.ALLOWED_HOSTS
    # )

    # Add request logging middleware
    application.add_middleware(RequestLoggingMiddleware)

    # Add auth middleware after CORS
    application.add_middleware(AuthMiddleware)

    # Add organization context middleware after auth
    application.add_middleware(OrgContextMiddleware)

    # Include API routes
    application.include_router(public_api_router, prefix=settings.API_V1_STR)
    application.include_router(protected_api_router, prefix=settings.API_V1_STR)

    # # Include frontend routes for shared resources
    # application.include_router(frontend_router)

    # Add global OPTIONS handler for CORS preflight requests
    @application.options("/{path:path}")
    async def options_handler(request: Request, path: str):
        """
        Global OPTIONS handler to support CORS preflight requests for all routes.
        This ensures that OPTIONS requests are properly handled even if the route
        doesn't explicitly define an OPTIONS method.
        """
        # Return an empty response with appropriate CORS headers
        # The CORS middleware will add the necessary headers
        return {}

    # Custom OpenAPI docs
    @application.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url="/openapi.json",
            title=f"{settings.PROJECT_NAME} - API Documentation",
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
        )

    @application.get("/openapi.json", include_in_schema=False)
    async def get_openapi_endpoint():
        return get_openapi(
            title=settings.PROJECT_NAME,
            version=settings.VERSION,
            description=settings.DESCRIPTION,
            routes=application.routes,
        )

    # Health check endpoint
    @application.get("/health")
    async def health_check():
        return {"status": "ok"}

    # Add global exception handler for HTTPException
    @application.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """
        Global exception handler for HTTPException.
        Ensures that HTTPExceptions are properly returned with their status codes.
        """
        logger.info(f"HTTPException: {exc.status_code} - {exc.detail}")

        # Create headers dictionary with CORS headers
        headers = {
            "Access-Control-Allow-Origin": request.headers.get(
                "origin", "http://localhost:8080"
            ),
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Expose-Headers": "*",
        }

        # Add exception headers if they exist
        if hasattr(exc, "headers") and exc.headers is not None:
            headers.update(exc.headers)

        return JSONResponse(
            status_code=exc.status_code, content={"detail": exc.detail}, headers=headers
        )

    # Add specific handler for JWT ExpiredSignatureError
    @application.exception_handler(jwt.ExpiredSignatureError)  # type: ignore
    async def expired_token_handler(request: Request, exc: jwt.ExpiredSignatureError):  # type: ignore
        """
        Exception handler for JWT ExpiredSignatureError.
        Ensures that expired tokens always return a 401 status code with proper CORS headers.
        """
        logger.warning(f"Token expired: {str(exc)}")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Token has expired"},
            headers={
                "Access-Control-Allow-Origin": request.headers.get(
                    "origin", "http://localhost:8080"
                ),
                "Access-Control-Allow-Credentials": "true",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Expose-Headers": "*",
                "WWW-Authenticate": "Bearer",
            },
        )

    # Add handler for our custom auth exceptions
    @application.exception_handler(AuthError)
    async def auth_exception_handler(request: Request, exc: AuthError):
        """
        Exception handler for custom auth exceptions.
        Ensures that auth errors are properly returned with their status codes and CORS headers.
        """
        logger.info(f"Auth error: {exc.status_code} - {exc.detail}")

        # Create headers dictionary with CORS headers
        headers = {
            "Access-Control-Allow-Origin": request.headers.get(
                "origin", "http://localhost:8080"
            ),
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Expose-Headers": "*",
        }

        # Add exception headers if they exist
        if hasattr(exc, "headers") and exc.headers is not None:
            headers.update(exc.headers)

        return JSONResponse(
            status_code=exc.status_code, content={"detail": exc.detail}, headers=headers
        )

    # Add global exception handler for all exceptions
    @application.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """
        Global exception handler for all exceptions.
        Logs unhandled exceptions and returns a 500 error with proper CORS headers.
        """
        # Check if it's a token-related exception
        error_message = str(exc)
        if "token" in error_message.lower() and "expire" in error_message.lower():
            logger.warning(f"Token expired exception: {error_message}")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Token has expired"},
                headers={
                    "Access-Control-Allow-Origin": request.headers.get(
                        "origin", "http://localhost:8080"
                    ),
                    "Access-Control-Allow-Credentials": "true",
                    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
                    "Access-Control-Allow-Headers": "*",
                    "Access-Control-Expose-Headers": "*",
                    "WWW-Authenticate": "Bearer",
                },
            )

        # Log the exception
        logger.error(f"Unhandled exception: {error_message}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"},
            headers={
                "Access-Control-Allow-Origin": request.headers.get(
                    "origin", "http://localhost:8080"
                ),
                "Access-Control-Allow-Credentials": "true",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Expose-Headers": "*",
            },
        )

    # Startup and shutdown events
    @application.on_event("startup")
    async def startup_db_client():
        await db.connect_to_database()
        await cache.connect_to_cache()
        application.state.db = db.db

        # Initialize and store services
        application.state.auth_service = await get_auth_service(db=db.db)  # type: ignore
        application.state.rbac_service = await get_rbac_service(db=db.db)  # type: ignore

        logger.info("Application startup complete")

    @application.on_event("shutdown")
    async def shutdown_db_client():
        # Cleanup services
        if hasattr(application.state, "auth_service"):
            await application.state.auth_service.cleanup()
        if hasattr(application.state, "rbac_service"):
            await application.state.rbac_service.cleanup()

        await db.close_database_connection()
        await cache.close_cache_connection()
        logger.info("Application shutdown complete")

    return application


app = create_application()
