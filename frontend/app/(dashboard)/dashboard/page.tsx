"use client"

import { useState } from "react"
import { DashboardHeader } from "@/components/header"
import { DashboardShell } from "@/components/shell"
import {
  SummaryTiles,
  ChartsSection,
  ActivityFeed,
  ShareThesisBlock,
  OnboardingFlow,
  QuickActions
} from "@/components/core/dashboard"
// Default empty data - will be replaced with API calls
const defaultStats = {
  activeDeals: 0,
  forms: 0,
  theses: 0,
  totalSubmissions: 0,
  avgScore: 0,
  topPerformer: null
}

const defaultSectorData = []
const defaultDealStagesData = []
const defaultActivityData = []
const defaultSharingData = {
  shareUrl: '',
  formTitle: 'Investment Form'
}

// Note: All authentication logic is preserved in the layout and auth context
// This component only handles UI rendering - no auth functions are removed

export default function DashboardPage() {
  const [showOnboarding, setShowOnboarding] = useState(false)

  // TODO: Replace with real API calls when backend is ready
  const hasData = defaultStats.activeDeals > 0 || defaultStats.forms > 0 || defaultStats.theses > 0

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Dashboard"
        text="AI-powered investment intelligence at your fingertips"
      />

      <div className="space-y-8">
        {/* Show onboarding for empty state */}
        {!hasData && (
          <OnboardingFlow
            onDismiss={() => setShowOnboarding(false)}
          />
        )}

        {/* Summary Tiles */}
        <SummaryTiles stats={defaultStats} />

        {/* Charts Section */}
        <ChartsSection
          sectorData={defaultSectorData}
          dealStagesData={defaultDealStagesData}
        />

        {/* Activity Feed and Share Block */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <ActivityFeed activities={defaultActivityData} />
          </div>
          <div className="lg:col-span-1">
            <ShareThesisBlock
              shareUrl={defaultSharingData.shareUrl}
              formTitle={defaultSharingData.formTitle}
            />
          </div>
        </div>

        {/* Quick Actions */}
        <QuickActions />
      </div>
    </DashboardShell>
  )
}
