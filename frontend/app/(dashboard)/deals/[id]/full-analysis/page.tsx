"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { motion } from "framer-motion"
import { Shell } from "@/components/shell"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  ChevronLeft, 
  Wand2, 
  Users, 
  TrendingUp, 
  Target,
  ExternalLink,
  Clock,
  Shield,
  Edit3
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { DealAPI } from "@/lib/api/deal-api"
import { DealDetailData, ThesisMatchBreakdown } from "@/lib/types/deal-detail"
import { useAuth } from "@/lib/auth-context"
import { ScoreOverrideModal } from "@/components/core/deals/deal-detail/score-override-modal"
import { OrbitAI } from "@/components/core/orbit-ai/orbit-ai"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { ThesisMatchTab } from "@/components/core/deals/full-analysis/thesis-match-tab"

export default function FullAnalysisPage() {
  const params = useParams()
  const dealId = params?.id as string
  const { isAuthenticated } = useAuth()
  
  const [dealData, setDealData] = useState<DealDetailData | null>(null)
  const [fullAnalysisData, setFullAnalysisData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('thesis')
  const [showOverrideModal, setShowOverrideModal] = useState(false)
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null)

  useEffect(() => {
    if (!isAuthenticated || !dealId || dealId === 'undefined') {
      if (dealId === 'undefined') {
        setError('Invalid deal ID. Please check the URL and try again.');
        setLoading(false);
      }
      return;
    }

    const fetchDealDetail = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log('Fetching full analysis for deal ID:', dealId);

        // Fetch both deal data and full analysis
        const [dealResponse, analysisResponse] = await Promise.all([
          DealAPI.getDeal(dealId),
          DealAPI.getFullAnalysis(dealId)
        ]);

        // Convert deal to DealDetailData format
        const dealDetailData: DealDetailData = {
          ...dealResponse,
          timeline: [],
          score_breakdown: analysisResponse.signal_breakdown ? {
            overall_score: analysisResponse.overall_score,
            signal_breakdown: analysisResponse.signal_breakdown,
            thesis_breakdown: analysisResponse.thesis_breakdown,
            scoring_version: "v2.0",
            last_updated: new Date().toISOString(),
            ai_summary: "Comprehensive AI-powered analysis",
            key_insights: [],
            is_overridden: false,
            override_history: []
          } : undefined,
          founders: [],
          external_signals: [],
          documents: [],
          chat_history: [],
          comprehensive_scoring: analysisResponse.comprehensive_scoring
        };

        setDealData(dealDetailData);
        setFullAnalysisData(analysisResponse);
      } catch (err: any) {
        console.error('Error fetching deal detail:', err)

        // Fallback to mock data
        try {
          const data = await DealDetailAPI.getDealDetail(dealId)
          setDealData(data)
        } catch (fallbackErr) {
          setError('Failed to load deal analysis. Please try again.')
        }
      } finally {
        setLoading(false)
      }
    }

    fetchDealDetail()
  }, [isAuthenticated, dealId])

  const handleScoreOverride = (signalType: string) => {
    setSelectedSignal(signalType)
    setShowOverrideModal(true)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  if (loading) {
    return (
      <Shell className="max-w-none">
        <div className="space-y-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/3" />
            <div className="h-12 bg-muted rounded w-2/3" />
            <div className="grid grid-cols-3 gap-4">
              <div className="h-32 bg-muted rounded" />
              <div className="h-32 bg-muted rounded" />
              <div className="h-32 bg-muted rounded" />
            </div>
          </div>
        </div>
      </Shell>
    )
  }

  if (error || !dealData) {
    return (
      <Shell className="max-w-none">
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="alertCircle" />
          <EmptyPlaceholder.Title>Error Loading Analysis</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            {error || 'Deal analysis could not be loaded.'}
          </EmptyPlaceholder.Description>
          <Link href={`/deals/${dealId}`}>
            <Button variant="outline">
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back to Deal
            </Button>
          </Link>
        </EmptyPlaceholder>
      </Shell>
    )
  }

  const scoreBreakdown = dealData.score_breakdown

  if (!scoreBreakdown) {
    return (
      <Shell className="max-w-none">
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="target" />
          <EmptyPlaceholder.Title>No Analysis Available</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            This deal hasn't been analyzed yet. Analysis will appear here once scoring is complete.
          </EmptyPlaceholder.Description>
          <Link href={`/deals/${dealId}`}>
            <Button variant="outline">
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back to Deal
            </Button>
          </Link>
        </EmptyPlaceholder>
      </Shell>
    )
  }

  return (
    <Shell className="max-w-none px-4 lg:px-8">
      <div className="space-y-8">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm text-muted-foreground">
          <Link
            href="/deals"
            className="flex items-center hover:text-foreground transition-colors"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to TractionX
          </Link>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Full Analysis – {dealData.company_name}
              </h1>
              <div className="flex items-center gap-3 mt-2">
                {/* <Badge variant="outline" className="gap-2">
                  <Wand2 className="h-3 w-3" />
                  Powered by AI
                </Badge> */}
                <Badge variant="secondary" className="gap-2">
                  <Clock className="h-3 w-3" />
                  Last updated {dealData.comprehensive_scoring?.metadata?.scored_at
                    ? new Date(dealData.comprehensive_scoring.metadata.scored_at * 1000).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })
                    : new Date(scoreBreakdown.last_updated).toLocaleDateString()
                  }
                </Badge>
                {scoreBreakdown.is_overridden && (
                  <Badge variant="outline" className="gap-2 border-orange-200 text-orange-700 bg-orange-50">
                    <Edit3 className="h-3 w-3" />
                    Score Overridden
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-5xl font-bold text-primary mb-1">
                {scoreBreakdown.overall_score}
              </div>
              <p className="text-sm text-muted-foreground">Overall Score</p>
            </div>
          </div>

          <Card className="border-0 bg-muted/30">
            <CardContent className="p-6">
              <p className="text-muted-foreground leading-relaxed">
                "{scoreBreakdown.ai_summary || 'Comprehensive AI-powered analysis of investment potential'}"
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 h-12 bg-muted/50">
            <TabsTrigger value="overview" className="gap-2">
              <Target className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="team" className="gap-2 opacity-50" disabled>
              <Users className="h-4 w-4" />
              Team Strength
              <Badge variant="outline" className="ml-2 text-xs">Soon</Badge>
            </TabsTrigger>
            <TabsTrigger value="market" className="gap-2 opacity-50" disabled>
              <TrendingUp className="h-4 w-4" />
              Market Signals
              <Badge variant="outline" className="ml-2 text-xs">Soon</Badge>
            </TabsTrigger>
            <TabsTrigger value="thesis" className="gap-2">
              <Shield className="h-4 w-4" />
              Thesis Match
            </TabsTrigger>
          </TabsList>

          <div className="mt-8">
            <TabsContent value="overview" className="space-y-6">
              {/* Overview content with comprehensive scoring details */}
              <div className="grid gap-6 md:grid-cols-3">
                {Object.entries(scoreBreakdown.signal_breakdown).map(([key, signal]) => (
                  <Card key={key} className="border-0 shadow-sm">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base capitalize">
                          {key.replace('_', ' ')}
                        </CardTitle>
                        <Badge className={cn("text-lg font-bold", getScoreColor(signal.score))}>
                          {signal.score}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        {signal.ai_insights || signal.explanation}
                      </p>

                      {/* Show AI-generated indicator */}
                      {key === 'thesis_match' && dealData?.comprehensive_scoring?.thesis && (
                        <div className="mb-3">
                          <div className="text-xs text-muted-foreground mb-1">
                            Thesis: {dealData.comprehensive_scoring.thesis.thesis_name}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Rules processed: {Object.keys(dealData.comprehensive_scoring.thesis.question_scores || {}).length}
                          </div>
                          {dealData.comprehensive_scoring.metadata?.ai_scoring_used && (
                            <Badge variant="secondary" className="text-xs mt-1">
                              AI Evaluated
                            </Badge>
                          )}
                        </div>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleScoreOverride(key)}
                        className="w-full"
                      >
                        <Edit3 className="h-3 w-3 mr-2" />
                        Override Score
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Detailed Thesis Breakdown */}
              {/* {dealData?.comprehensive_scoring?.thesis && (
                <Card className="border-0 shadow-sm mt-6">
                  <CardHeader>
                    <CardTitle className="text-lg">Detailed Thesis Analysis</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Question-by-question scoring breakdown
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(dealData.comprehensive_scoring.thesis.question_scores || {}).map(([questionId, scoreData]) => (
                        <div key={questionId} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">Question {questionId}</div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {(scoreData.raw_score * 100).toFixed(0)}%
                              </Badge>
                              <Badge variant="secondary">
                                Weight: {scoreData.weight}
                              </Badge>
                              {scoreData.ai_generated && (
                                <Badge variant="secondary" className="text-xs">
                                  AI
                                </Badge>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {scoreData.explanation}
                          </p>
                          <div className="text-xs text-muted-foreground">
                            Type: {scoreData.question_type} |
                            Weighted Score: {scoreData.weighted_score.toFixed(2)}
                            {scoreData.aggregation_used && ` | Aggregation: ${scoreData.aggregation_type}`}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )} */}
            </TabsContent>

            {/* Other tab contents will be added in next iteration */}
            <TabsContent value="team">
              <Card>
                <CardContent className="p-6">
                  <p className="text-muted-foreground">Team analysis coming soon...</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="market">
              <Card>
                <CardContent className="p-6">
                  <p className="text-muted-foreground">Market analysis coming soon...</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="thesis">
              <ThesisMatchTab deal={dealData} fullAnalysisData={fullAnalysisData} />
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Score Override Modal */}
      <ScoreOverrideModal
        open={showOverrideModal}
        onOpenChange={setShowOverrideModal}
        dealId={dealId}
        signalType={selectedSignal}
        currentScore={selectedSignal ? scoreBreakdown.signal_breakdown[selectedSignal]?.score : 0}
        onSuccess={() => {
          // Refresh deal data
          window.location.reload()
        }}
      />

      {/* Orbit AI - Floating Glassmorphic Assistant */}
      <OrbitAI
        dealId={dealId}
        dealContext={dealData}
      />
    </Shell>
  )
}
