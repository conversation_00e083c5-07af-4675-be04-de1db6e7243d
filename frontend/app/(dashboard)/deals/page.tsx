"use client"

import { useState, useEffect, useMemo } from "react"
import { Shell } from "@/components/shell"
import { DealsGrid, DealsHeader } from "@/components/core/deals"
import { NewDealModal } from "@/components/core/deals/new-deal-modal"
import { DealAPI } from "@/lib/api/deal-api"
import { Deal, DealStatus } from "@/lib/types/deal"
import { useAuth } from "@/lib/auth-context"
import { useToast } from "@/components/ui/use-toast"
// Mock deals removed - will use API data only

export default function DealsPage() {
  const { isAuthenticated } = useAuth()
  const { toast } = useToast()
  const [allDeals, setAllDeals] = useState<Deal[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeFilter, setActiveFilter] = useState('all')
  const [showNewDealModal, setShowNewDealModal] = useState(false)

  useEffect(() => {
    const fetchDeals = async () => {
      if (!isAuthenticated) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        // Try to fetch from API first
        const response = await DealAPI.listDeals(0, 100)
        setAllDeals(response.deals || [])
      } catch (err: any) {
        console.error('Error fetching deals:', err)
        setError('Failed to load deals')
        setAllDeals([])
      } finally {
        setLoading(false)
      }
    }

    fetchDeals()
  }, [isAuthenticated])

  // Filter and search deals
  const filteredDeals = useMemo(() => {
    let filtered = allDeals

    // Apply status filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(deal => deal.status === activeFilter)
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(deal =>
        deal.company_name?.toLowerCase().includes(query) ||
        (Array.isArray(deal.sector)
          ? deal.sector.some(s => s.toLowerCase().includes(query))
          : deal.sector?.toLowerCase().includes(query)) ||
        deal.stage?.toLowerCase().includes(query)
      )
    }

    return filtered
  }, [allDeals, activeFilter, searchQuery])

  const handleSearchChange = (search: string) => {
    setSearchQuery(search)
  }

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
  }

  // Calculate deal counts for filters
  const dealCounts = useMemo(() => ({
    all: allDeals.length,
    new: allDeals.filter(d => d.status === DealStatus.NEW).length,
    triage: allDeals.filter(d => d.status === DealStatus.TRIAGE).length,
    reviewed: allDeals.filter(d => d.status === DealStatus.REVIEWED).length,
    approved: allDeals.filter(d => d.status === DealStatus.APPROVED).length,
    excluded: allDeals.filter(d => d.status === DealStatus.EXCLUDED).length,
    closed: allDeals.filter(d => d.status === DealStatus.CLOSED).length,
  }), [allDeals])

  const handleNewDeal = () => {
    setShowNewDealModal(true)
  }

  const handleCreateDeal = async (dealData: any) => {
    try {
      // TODO: Replace with actual API call
      // const newDeal = await DealAPI.createDeal(dealData)

      // Mock new deal creation
      const newDeal: Deal = {
        id: `deal_${Date.now()}`,
        org_id: 'demo-org',
        form_id: 'demo-form',
        submission_ids: [],
        company_name: dealData.company_name,
        stage: dealData.stage,
        sector: dealData.sector ? [dealData.sector] : undefined,
        company_website: dealData.company_website,
        status: DealStatus.NEW,
        notes: dealData.notes,
        created_by: 'demo-user',
        created_at: Date.now(),
        updated_at: Date.now()
      }

      setAllDeals(prev => [newDeal, ...prev])

      toast({
        title: 'Success',
        description: `Deal "${dealData.company_name}" created successfully.`
      })

      // TODO: If pitch deck was uploaded, process it
      if (dealData.pitch_deck) {
        toast({
          title: 'Processing',
          description: 'Pitch deck uploaded. We\'ll send an email to the startup to complete their submission.'
        })
      }
    } catch (error) {
      console.error('Error creating deal:', error)
      toast({
        title: 'Error',
        description: 'Failed to create deal. Please try again.',
        variant: 'destructive'
      })
      throw error
    }
  }

  return (
    <Shell className="max-w-none">
      <div className="space-y-8">
        <DealsHeader
          onSearchChange={handleSearchChange}
          onFilterChange={handleFilterChange}
          activeFilter={activeFilter}
          totalDeals={allDeals.length}
          dealCounts={dealCounts}
        />

        <DealsGrid
          deals={filteredDeals}
          loading={loading}
          error={error}
          onNewDeal={handleNewDeal}
        />

        {/* New Deal Modal */}
        <NewDealModal
          open={showNewDealModal}
          onOpenChange={setShowNewDealModal}
          onSubmit={handleCreateDeal}
        />
      </div>
    </Shell>
  )
}
