import { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"

import { AuthLayout } from "@/components/auth/auth-layout"
import { ForgotPasswordForm } from "@/components/auth/forgot-password-form"

export const metadata: Metadata = {
  title: "Reset your password - TractionX",
  description: "Reset your TractionX account password to regain access to your investment platform.",
}

export default function ForgotPasswordPage() {
  return (
    <AuthLayout
      title="Reset your password"
      subtitle="We'll help you get back into your account"
    >
      <Suspense fallback={
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      }>
        <ForgotPasswordForm />
      </Suspense>
    </AuthLayout>
  )
}
