import { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"

import { AuthLayout } from "@/components/auth/auth-layout"
import { LoginForm } from "@/components/auth/login-form"

export const metadata: Metadata = {
  title: "Sign in to TractionX",
  description: "Access your TractionX account to manage deals, analyze investments, and leverage AI-powered insights.",
}

export default function LoginPage() {
  return (
    <AuthLayout
      title="Welcome back"
      subtitle="Enter your credentials to access your account"
    >
      <Suspense fallback={
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      }>
        <LoginForm />
      </Suspense>
    </AuthLayout>
  )
}
