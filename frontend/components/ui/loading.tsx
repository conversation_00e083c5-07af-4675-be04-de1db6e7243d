"use client"

import { useEffect, useRef } from "react"
import { cn } from "@/lib/utils"

// @ts-ignore - anime.js types are problematic
const anime = require("animejs")

interface LoadingProps {
  className?: string
  size?: "sm" | "md" | "lg"
  fullScreen?: boolean
  text?: string
}

export function Loading({ 
  className, 
  size = "md", 
  fullScreen = false, 
  text = "Loading..." 
}: LoadingProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<any>(null)

  useEffect(() => {
    if (!containerRef.current) return

    // Find squares within the current container
    const squares = containerRef.current?.querySelectorAll('.square')
    
    if (squares && squares.length > 0) {
      animationRef.current = anime({
        targets: squares,
        rotate: 90,
        loop: true,
        easing: 'easeInOutExpo',
        duration: 1200,
        direction: 'alternate'
      })
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.pause()
      }
    }
  }, [])

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8"
  }

  const containerSizeClasses = {
    sm: "gap-1",
    md: "gap-2",
    lg: "gap-3"
  }

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col items-center gap-4">
          <div 
            ref={containerRef}
            className={cn(
              "flex items-center justify-center",
              containerSizeClasses[size]
            )}
          >
            <div className={cn("square bg-foreground/20 rounded-sm", sizeClasses[size])} />
            <div className={cn("square bg-foreground/40 rounded-sm", sizeClasses[size])} />
            <div className={cn("square bg-foreground/60 rounded-sm", sizeClasses[size])} />
            <div className={cn("square bg-foreground/80 rounded-sm", sizeClasses[size])} />
          </div>
          {text && (
            <p className="text-sm text-muted-foreground font-medium">{text}</p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div 
        ref={containerRef}
        className={cn(
          "flex items-center justify-center",
          containerSizeClasses[size]
        )}
      >
        <div className={cn("square bg-foreground/20 rounded-sm", sizeClasses[size])} />
        <div className={cn("square bg-foreground/40 rounded-sm", sizeClasses[size])} />
        <div className={cn("square bg-foreground/60 rounded-sm", sizeClasses[size])} />
        <div className={cn("square bg-foreground/80 rounded-sm", sizeClasses[size])} />
      </div>
    </div>
  )
}

// Minimal inline loading component
export function LoadingDots({ className }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<any>(null)

  useEffect(() => {
    if (!containerRef.current) return

    // Find squares within the current container
    const squares = containerRef.current?.querySelectorAll('.square')
    
    if (squares && squares.length > 0) {
      animationRef.current = anime({
        targets: squares,
        rotate: 90,
        loop: true,
        easing: 'easeInOutExpo',
        duration: 800,
        direction: 'alternate'
      })
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.pause()
      }
    }
  }, [])

  return (
    <div ref={containerRef} className={cn("flex items-center gap-1", className)}>
      <div className="square w-2 h-2 bg-current rounded-sm opacity-60" />
      <div className="square w-2 h-2 bg-current rounded-sm opacity-70" />
      <div className="square w-2 h-2 bg-current rounded-sm opacity-80" />
      <div className="square w-2 h-2 bg-current rounded-sm opacity-90" />
    </div>
  )
}

// Page loading component
export function PageLoading({ text = "Loading page..." }: { text?: string }) {
  return <Loading fullScreen={true} size="lg" text={text} />
} 