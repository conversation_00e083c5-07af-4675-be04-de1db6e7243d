"use client"

import React from 'react';
import { 
  ScoringRule, 
  RuleType, 
  FilterCondition,
  CompoundFilter,
  getOperatorDisplay,
  getAggregationDisplay,
  LogicalOperator,
  ConditionOperator
} from '@/lib/types/thesis';

interface RuleSummaryProps {
  rule: Partial<ScoringRule>;
  questionLabel?: string;
}

function formatConditionValue(value: any): string {
  // Handle text question scoring with good/bad references
  if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
    if (value.good_reference || value.bad_reference) {
      const parts: string[] = [];
      if (value.good_reference) {
        parts.push(`Good: "${value.good_reference.substring(0, 50)}${value.good_reference.length > 50 ? '...' : ''}"`);
      }
      if (value.bad_reference) {
        parts.push(`Bad: "${value.bad_reference.substring(0, 50)}${value.bad_reference.length > 50 ? '...' : ''}"`);
      }
      return parts.join(' | ');
    }
    // <PERSON>le legacy good_answers/bad_answers
    if (value.good_answers || value.bad_answers) {
      const parts: string[] = [];
      if (value.good_answers) {
        parts.push(`Good: "${value.good_answers.substring(0, 50)}${value.good_answers.length > 50 ? '...' : ''}"`);
      }
      if (value.bad_answers) {
        parts.push(`Bad: "${value.bad_answers.substring(0, 50)}${value.bad_answers.length > 50 ? '...' : ''}"`);
      }
      return parts.join(' | ');
    }
    return JSON.stringify(value);
  }

  if (Array.isArray(value)) {
    return value.join(', ');
  }
  if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
    // Try to parse JSON string array
    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        return parsed.join(', ');
      }
    } catch {
      // If parsing fails, treat as regular string
    }
  }
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  if (value === null || value === undefined || value === '') {
    return '[not set]';
  }
  return String(value);
}

function summarizeFilterCondition(condition: FilterCondition, questionLabel?: string): string {
  const label = questionLabel || `Question ${condition.question_id}`;

  // For text questions with good/bad references, show special summary
  if (typeof condition.value === 'object' && condition.value !== null && !Array.isArray(condition.value)) {
    if (condition.value.good_reference || condition.value.bad_reference) {
      return `${label} scored using AI with reference examples`;
    }
    if (condition.value.good_answers || condition.value.bad_answers) {
      return `${label} scored using AI with reference examples`;
    }
  }

  const operator = getOperatorDisplay(condition.operator || ConditionOperator.EQUALS);
  const value = formatConditionValue(condition.value);

  return `${label} ${operator} ${value}`;
}

function summarizeCompoundCondition(condition: CompoundFilter): string {
  if (!condition.conditions || condition.conditions.length === 0) {
    return '[no conditions]';
  }

  const conditionSummaries = condition.conditions.map(cond => {
    if ('question_id' in cond) {
      return summarizeFilterCondition(cond);
    } else {
      return `(${summarizeCompoundCondition(cond)})`;
    }
  });

  const operator = condition.operator === LogicalOperator.AND ? ' AND ' : 
                   condition.operator === LogicalOperator.OR ? ' OR ' : 
                   ' NOT ';

  if (condition.operator === LogicalOperator.NOT && conditionSummaries.length === 1) {
    return `NOT ${conditionSummaries[0]}`;
  }

  return conditionSummaries.join(operator);
}

export function RuleSummary({ rule, questionLabel }: RuleSummaryProps) {
  if (!rule.rule_type) {
    return <span className="text-muted-foreground italic">Incomplete rule configuration</span>;
  }

  // Handle scoring rules
  if (rule.rule_type === RuleType.SCORING) {
    if (!rule.condition) {
      return <span className="text-muted-foreground italic">No condition specified</span>;
    }

    let conditionSummary: string;
    if ('question_id' in rule.condition) {
      conditionSummary = summarizeFilterCondition(rule.condition, questionLabel);
    } else {
      conditionSummary = summarizeCompoundCondition(rule.condition);
    }

    const weight = rule.weight || 1;
    let summary = `Award ${weight} point${weight !== 1 ? 's' : ''} if ${conditionSummary}`;

    // Add aggregation info for repeatable sections
    if (rule.aggregation && rule.section_id) {
      const aggregationType = getAggregationDisplay(rule.aggregation);
      summary += ` (${aggregationType}`;
      
      if (rule.value_field) {
        summary += ` of ${rule.value_field}`;
      }
      
      if (rule.aggregate_operator && rule.aggregate_threshold) {
        summary += ` ${getOperatorDisplay(rule.aggregate_operator)} ${rule.aggregate_threshold}`;
      }
      
      summary += ')';
    }

    // Add text scoring info if defined (check both new and legacy formats)
    const hasTextScoring = rule.good_answers || rule.bad_answers ||
      (rule.condition && 'value' in rule.condition &&
       typeof rule.condition.value === 'object' &&
       (rule.condition.value?.good_reference || rule.condition.value?.bad_reference));

    if (hasTextScoring) {
      summary += ' • AI text scoring enabled';
    }

    return (
      <span>
        {summary}
        {hasTextScoring && (
          <div className="text-xs text-muted-foreground mt-1 space-y-1">
            {(rule.good_answers || (rule.condition && 'value' in rule.condition && rule.condition.value?.good_reference)) && (
              <div>✓ Good examples defined</div>
            )}
            {(rule.bad_answers || (rule.condition && 'value' in rule.condition && rule.condition.value?.bad_reference)) && (
              <div>✗ Bad examples defined</div>
            )}
          </div>
        )}
      </span>
    );
  }

  // Handle bonus rules
  if (rule.rule_type === RuleType.BONUS) {
    if (!rule.condition) {
      return <span className="text-muted-foreground italic">No condition specified</span>;
    }

    const bonusPoints = rule.bonus_points || 0;
    let conditionSummary: string;

    if ('question_id' in rule.condition) {
      conditionSummary = summarizeFilterCondition(rule.condition, questionLabel);
    } else {
      conditionSummary = summarizeCompoundCondition(rule.condition);
    }

    return (
      <span>
        Award <strong>+{bonusPoints}</strong> bonus point{bonusPoints !== 1 ? 's' : ''} if {conditionSummary}
      </span>
    );
  }

  return <span className="text-muted-foreground italic">Unknown rule type</span>;
}

// Helper component for displaying rule summaries in lists
export function RuleSummaryBadge({ rule, questionLabel, className }: RuleSummaryProps & { className?: string }) {
  return (
    <div className={`text-xs text-muted-foreground bg-muted/50 rounded px-2 py-1 ${className || ''}`}>
      <RuleSummary rule={rule} questionLabel={questionLabel} />
    </div>
  );
}

// Helper component for compact rule display
export function CompactRuleSummary({ rule, questionLabel }: RuleSummaryProps) {
  if (!rule.rule_type || !rule.condition) {
    return <span className="text-muted-foreground italic text-xs">Incomplete</span>;
  }

  if (rule.rule_type === RuleType.SCORING) {
    const weight = rule.weight || 1;
    return (
      <span className="text-xs">
        <span className="font-medium">{weight}pt</span> if condition met
      </span>
    );
  }

  if (rule.rule_type === RuleType.BONUS) {
    const bonusPoints = rule.bonus_points || 0;
    return (
      <span className="text-xs">
        <span className="font-medium text-green-600">+{bonusPoints}pt</span> bonus
      </span>
    );
  }

  return null;
}
