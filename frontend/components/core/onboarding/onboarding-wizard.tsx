"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { AuthCard } from "@/components/auth/auth-card"
import { OrganizationSetupForm } from "./organization-setup-form"
import { ProfileSetupForm } from "./profile-setup-form"
import { WelcomeScreen } from "./welcome-screen"
import { ValidateInviteCodeResponse } from "@/lib/api/onboarding-api"

interface OnboardingWizardProps {
  inviteCode: string
  inviteData: ValidateInviteCodeResponse
  onComplete: (result: any) => void
}

const STEPS = [
  {
    id: 1,
    title: "Create Your Organization",
    description: "Set up your organization details and branding",
  },
  {
    id: 2,
    title: "Create Your Profile",
    description: "Set up your personal profile and account",
  },
  {
    id: 3,
    title: "Welcome!",
    description: "You're all set to start using TractionX",
  },
]

export function OnboardingWizard({ inviteCode, inviteData, onComplete }: OnboardingWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [stepData, setStepData] = useState<Record<number, any>>({})

  const progress = (currentStep / STEPS.length) * 100

  const handleStepComplete = (stepNumber: number, data: any) => {
    setStepData(prev => ({ ...prev, [stepNumber]: data }))
    
    if (stepNumber < STEPS.length) {
      setCurrentStep(stepNumber + 1)
    } else {
      // Final step completed
      onComplete(data)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const currentStepData = STEPS.find(step => step.id === currentStep)

  return (
    <div className="w-full max-w-2xl mx-auto space-y-8">
      {/* Progress Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        {/* Step Indicator */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">
            Step {currentStep} of {STEPS.length}
          </span>
          <span className="text-muted-foreground">
            {Math.round(progress)}% Complete
          </span>
        </div>

        {/* Progress Bar */}
        <Progress value={progress} className="h-2" />

        {/* Step Title */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold tracking-tight">
            {currentStepData?.title}
          </h2>
          <p className="text-muted-foreground">
            {currentStepData?.description}
          </p>
        </div>
      </motion.div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          <AuthCard>
            {currentStep === 1 && (
              <OrganizationSetupForm
                inviteCode={inviteCode}
                inviteData={inviteData}
                onComplete={(data) => handleStepComplete(1, data)}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            )}

            {currentStep === 2 && (
              <ProfileSetupForm
                inviteCode={inviteCode}
                inviteData={inviteData}
                organizationData={stepData[1]}
                onComplete={(data) => handleStepComplete(2, data)}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            )}

            {currentStep === 3 && (
              <WelcomeScreen
                userData={stepData[2]}
                organizationData={stepData[1]}
                onComplete={(data) => handleStepComplete(3, data)}
              />
            )}
          </AuthCard>
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      {currentStep < 3 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-between"
        >
          <Button
            variant="ghost"
            onClick={handleBack}
            disabled={currentStep === 1 || isLoading}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Back
          </Button>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {STEPS.map((step, index) => (
              <div
                key={step.id}
                className={`h-2 w-2 rounded-full transition-colors ${
                  step.id <= currentStep
                    ? "bg-primary"
                    : "bg-muted"
                }`}
              />
            ))}
          </div>
        </motion.div>
      )}
    </div>
  )
}
