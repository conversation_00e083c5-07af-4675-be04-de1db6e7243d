"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { Building2, Globe, Upload, Check, X } from "lucide-react"
import { toast } from "sonner"
import * as z from "zod"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { ValidateInviteCodeResponse } from "@/lib/api/onboarding-api"
import OnboardingAPI from "@/lib/api/onboarding-api"

const organizationSchema = z.object({
  org_name: z.string().min(2, "Organization name must be at least 2 characters").max(100),
  subdomain: z.string()
    .min(3, "Subdomain must be at least 3 characters")
    .max(50)
    .regex(/^[a-z0-9-]+$/, "Subdomain can only contain lowercase letters, numbers, and hyphens"),
  website: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  logo_url: z.string().optional(),
})

type OrganizationFormData = z.infer<typeof organizationSchema>

interface OrganizationSetupFormProps {
  inviteCode: string
  inviteData: ValidateInviteCodeResponse
  onComplete: (data: any) => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
}

export function OrganizationSetupForm({
  inviteCode,
  inviteData,
  onComplete,
  isLoading,
  setIsLoading,
}: OrganizationSetupFormProps) {
  const [subdomainStatus, setSubdomainStatus] = useState<'idle' | 'checking' | 'available' | 'unavailable'>('idle')
  const [subdomainCheckTimeout, setSubdomainCheckTimeout] = useState<NodeJS.Timeout | null>(null)

  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      org_name: inviteData.org_name || "",
      subdomain: "",
      website: "",
      logo_url: "",
    },
  })

  const { watch, setValue } = form

  const orgName = watch("org_name")
  const subdomain = watch("subdomain")

  // Auto-generate subdomain from org name
  useEffect(() => {
    if (orgName && !subdomain) {
      const generatedSubdomain = orgName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "")
        .substring(0, 50)
      
      setValue("subdomain", generatedSubdomain)
    }
  }, [orgName, subdomain, setValue])

  // Check subdomain availability with debouncing
  useEffect(() => {
    if (subdomain && subdomain.length >= 3) {
      if (subdomainCheckTimeout) {
        clearTimeout(subdomainCheckTimeout)
      }

      const timeout = setTimeout(async () => {
        try {
          setSubdomainStatus('checking')
          const response = await OnboardingAPI.checkSubdomainAvailability(subdomain)
          setSubdomainStatus(response.available ? 'available' : 'unavailable')
        } catch (error) {
          console.error("Subdomain check error:", error)
          setSubdomainStatus('idle')
        }
      }, 500)

      setSubdomainCheckTimeout(timeout)
    } else {
      setSubdomainStatus('idle')
    }

    return () => {
      if (subdomainCheckTimeout) {
        clearTimeout(subdomainCheckTimeout)
      }
    }
  }, [subdomain])

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      setIsLoading(true)

      // Final subdomain check
      if (subdomainStatus !== 'available') {
        toast.error("Please choose an available subdomain")
        return
      }

      const response = await OnboardingAPI.onboardingStepOne(inviteCode, {
        org_name: data.org_name,
        subdomain: data.subdomain,
        website: data.website || undefined,
        logo_url: data.logo_url || undefined,
      })

      toast.success("Organization created successfully!")
      onComplete(response)
    } catch (error: any) {
      console.error("Organization setup error:", error)
      toast.error(error.message || "Failed to create organization")
    } finally {
      setIsLoading(false)
    }
  }

  const getSubdomainIcon = () => {
    switch (subdomainStatus) {
      case 'checking':
        return <motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Infinity, ease: "linear" }} className="h-4 w-4 border-2 border-muted-foreground border-t-transparent rounded-full" />
      case 'available':
        return <Check className="h-4 w-4 text-green-500" />
      case 'unavailable':
        return <X className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Organization Name */}
        <FormField
          control={form.control}
          name="org_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Organization Name
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter your organization name"
                  {...field}
                  className="h-12 rounded-xl border-2 focus:border-primary transition-colors"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Subdomain */}
        <FormField
          control={form.control}
          name="subdomain"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Subdomain</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="your-organization"
                    {...field}
                    className="h-12 rounded-xl border-2 focus:border-primary transition-colors pr-10"
                  />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    {getSubdomainIcon()}
                  </div>
                </div>
              </FormControl>
              <div className="text-sm text-muted-foreground">
                Your organization will be available at: <span className="font-mono">{subdomain}.tractionx.ai</span>
              </div>
              {subdomainStatus === 'unavailable' && (
                <div className="text-sm text-red-500">
                  This subdomain is not available. Please choose another.
                </div>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Website */}
        <FormField
          control={form.control}
          name="website"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Website (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="https://your-website.com"
                  {...field}
                  className="h-12 rounded-xl border-2 focus:border-primary transition-colors"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Logo Upload Placeholder */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Logo (Optional)
          </Label>
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-xl p-8 text-center">
            <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">
              Drag & drop your logo here, or click to browse
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              PNG, JPG up to 5MB
            </p>
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isLoading || subdomainStatus !== 'available'}
          className="w-full h-12 rounded-xl font-semibold"
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"
            />
          ) : null}
          {isLoading ? "Creating Organization..." : "Continue"}
        </Button>
      </form>
    </Form>
  )
}
