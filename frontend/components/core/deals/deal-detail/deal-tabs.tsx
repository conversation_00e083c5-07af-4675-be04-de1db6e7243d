"use client"

import { motion } from "framer-motion"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { DealDetailData } from "@/lib/types/deal-detail"
import { TimelineTab } from "./timeline-tab"
import { ScoreTab } from "./score-tab"
import { FoundersTab } from "./founders-tab"
import { SignalsTab } from "./signals-tab"
import { DocumentsTab } from "./documents-tab"
import { BenchmarksTab } from "./benchmarks-tab"
import { 
  Clock, 
  Target, 
  Users, 
  Radio, 
  FileText, 
  BarChart3 
} from "lucide-react"

interface DealTabsProps {
  deal: DealDetailData
  activeTab: string
  onTabChange: (tab: string) => void
}

const tabs = [
  {
    id: 'score',
    label: 'Score',
    icon: Target,
    component: ScoreTab
  },
  {
    id: 'founders',
    label: 'Founders',
    icon: Users,
    component: FoundersTab
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    component: DocumentsTab
  },
  {
    id: 'signals',
    label: 'External Signals',
    icon: Radio,
    component: SignalsTab,
    disabled: true
  },
  {
    id: 'benchmarks',
    label: 'Benchmarks',
    icon: BarChart3,
    component: BenchmarksTab,
    disabled: true
  },
  {
    id: 'timeline',
    label: 'Timeline',
    icon: Clock,
    component: TimelineTab
  }
]

export function DealTabs({ deal, activeTab, onTabChange }: DealTabsProps) {
  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'timeline':
        return deal.timeline?.length || 0
      case 'founders':
        return deal.founders?.length || 0
      case 'signals':
        return deal.external_signals?.length || 0
      case 'documents':
        return deal.documents?.length || 0
      default:
        return null
    }
  }

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        {/* Full-Width Tab Navigation - Edge to Edge */}
        <div className="w-full border-b bg-white/50 backdrop-blur">
          <TabsList className="w-full grid grid-cols-6 h-auto p-0 bg-transparent border-0 rounded-none">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const count = getTabCount(tab.id)
              
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  disabled={tab.disabled}
                  className="flex items-center justify-center gap-3 py-6 px-4 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-primary rounded-none bg-transparent hover:bg-gray-50/50 transition-all duration-200 font-medium text-gray-600 hover:text-gray-900"
                >
                  <Icon className="h-5 w-5" />
                  <span className="text-sm font-medium">{tab.label}</span>
                  
                  {count !== null && count > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="h-5 min-w-[20px] text-xs px-1.5 rounded-full bg-gray-100 text-gray-700 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                    >
                      {count}
                    </Badge>
                  )}
                  
                  {tab.disabled && (
                    <Badge 
                      variant="outline" 
                      className="h-5 text-xs px-2 text-gray-400 border-gray-300 rounded-full"
                    >
                      Soon
                    </Badge>
                  )}
                </TabsTrigger>
              )
            })}
          </TabsList>
        </div>

        {/* Full Width Tab Content - Consistent Container */}
        <div className="w-full">
          {tabs.map((tab) => {
            const Component = tab.component
            
            return (
              <TabsContent 
                key={tab.id} 
                value={tab.id}
                className="mt-0 focus-visible:outline-none w-full min-h-screen"
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full px-8 py-6"
                >
                  <Component deal={deal} />
                </motion.div>
              </TabsContent>
            )
          })}
        </div>
      </Tabs>
    </div>
  )
}
