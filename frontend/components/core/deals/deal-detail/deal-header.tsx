"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  ChevronLeft,
  FileText,
  Share2,
  MessageSquare,
  MoreHorizontal,
  Star,
  Flag,
  Zap,
  StickyNote,
  File,
  Radio,
  Clock
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface DealHeaderProps {
  deal: DealDetailData
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-50/50 text-blue-600 border-blue-200/50 before:bg-blue-500'
    case 'active':
      return 'bg-green-50/50 text-green-600 border-green-200/50 before:bg-green-500'
    case 'triage':
      return 'bg-yellow-50/50 text-yellow-600 border-yellow-200/50 before:bg-yellow-500'
    case 'completed':
      return 'bg-gray-50/50 text-gray-600 border-gray-200/50 before:bg-gray-500'
    case 'flagged':
      return 'bg-red-50/50 text-red-600 border-red-200/50 before:bg-red-500'
    case 'hard_pass':
      return 'bg-red-50/50 text-red-600 border-red-200/50 before:bg-red-500'
    default:
      return 'bg-gray-50/50 text-gray-600 border-gray-200/50 before:bg-gray-500'
  }
}

const getStageColor = (stage: string) => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-purple-50/50 text-purple-600 border-purple-200/50'
    case 'seed':
      return 'bg-blue-50/50 text-blue-600 border-blue-200/50'
    case 'series a':
      return 'bg-green-50/50 text-green-600 border-green-200/50'
    case 'series b':
      return 'bg-orange-50/50 text-orange-600 border-orange-200/50'
    case 'series c':
      return 'bg-red-50/50 text-red-600 border-red-200/50'
    default:
      return 'bg-gray-50/50 text-gray-600 border-gray-200/50'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const formatTimeAgo = (timestamp: string | number) => {
  const date = new Date(typeof timestamp === 'string' ? timestamp : timestamp * 1000)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

export function DealHeader({ deal }: DealHeaderProps) {
  const [isStarred, setIsStarred] = useState(false)

  const handleStarToggle = () => {
    setIsStarred(!isStarred)
    // TODO: Implement star/bookmark functionality
  }

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share deal:', deal.id)
  }

  const handleCreateMemo = () => {
    // TODO: Implement deal memo creation
    console.log('Create memo for deal:', deal.id)
  }

  const overallScore = deal.score_breakdown?.overall_score || 0
  const foundersCount = deal.founders?.length || 0
  const documentsCount = deal.documents?.length || 0
  const signalsCount = deal.external_signals?.length || 0
  const lastUpdated = formatTimeAgo(deal.updated_at)

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-muted-foreground">
        <Link
          href="/deals"
          className="flex items-center hover:text-foreground transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to Deals
        </Link>
      </div>

      {/* Header ("Hero Row") - PRD Specification */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        {/* Left Side */}
        <div className="space-y-4 flex-1">
          {/* Deal Name - Large, bold, left-aligned */}
          <h1 className="text-4xl font-bold tracking-tight text-left">
            {deal.company_name || 'Unnamed Company'}
          </h1>

          {/* Chips (single row) - PRD Specification */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Status (colored dot, muted chip) */}
            <Badge
              variant="outline"
              className={cn(
                "relative pl-6 font-medium border-0 bg-muted/50",
                getStatusColor(deal.status),
                "before:absolute before:left-2 before:top-1/2 before:-translate-y-1/2 before:w-2 before:h-2 before:rounded-full"
              )}
            >
              {deal.status.charAt(0).toUpperCase() + deal.status.slice(1).replace('_', ' ')}
            </Badge>

            {/* Stage (gray chip) */}
            {deal.stage && (
              <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-0">
                {deal.stage}
              </Badge>
            )}

            {/* Sector(s) (max 2, gray chip, "+N more" hover) */}
            {deal.sector && (
              <>
                {Array.isArray(deal.sector) ? (
                  <>
                    <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-0">
                      {deal.sector[0]}
                    </Badge>
                    {deal.sector.length > 1 && (
                      <Badge
                        variant="secondary"
                        className="bg-gray-100 text-gray-700 border-0 cursor-pointer"
                        title={deal.sector.slice(1).join(', ')}
                      >
                        {deal.sector.length === 2 ? deal.sector[1] : `+${deal.sector.length - 1} more`}
                      </Badge>
                    )}
                  </>
                ) : (
                  <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-0">
                    {deal.sector}
                  </Badge>
                )}
              </>
            )}

            {/* Score (circular or pill badge, large, minimal) */}
            {overallScore > 0 && (
              <Badge
                variant="outline"
                className={cn(
                  "px-3 py-1 text-lg font-bold border-2 bg-white",
                  overallScore >= 80 ? "border-green-500 text-green-700" :
                  overallScore >= 60 ? "border-yellow-500 text-yellow-700" :
                  "border-red-500 text-red-700"
                )}
              >
                {overallScore}
              </Badge>
            )}
          </div>

          {/* Meta line: Below chips, smaller font, secondary color */}
          <div className="text-sm text-muted-foreground">
            {foundersCount} founder{foundersCount !== 1 ? 's' : ''} · {documentsCount} document{documentsCount !== 1 ? 's' : ''} · {signalsCount} signal{signalsCount !== 1 ? 's' : ''} · Last updated {lastUpdated}
          </div>
        </div>

        {/* Right Side - Action Buttons (row-aligned) */}
        <div className="flex items-center gap-5">
          {/* Create Memo (main CTA, "Powered by AI" subtitle, accent color) */}
          <div className="flex flex-col items-center">
            <Button
              onClick={handleCreateMemo}
              className="gap-2 bg-primary hover:bg-primary/90"
            >
              <StickyNote className="h-4 w-4" />
              Create Memo
            </Button>
            {/* <span className="text-xs text-muted-foreground mt-1">Powered by AI</span> */}
          </div>

          {/* Share (icon + label, minimal) */}
          <Button
            variant="ghost"
            onClick={handleShare}
            className="gap-2"
          >
            <Share2 className="h-4 w-4" />
            Share
          </Button>

          {/* Open Inbox (icon + label, minimal) */}
          <Button
            variant="ghost"
            className="gap-2"
          >
            <MessageSquare className="h-4 w-4" />
            Open Inbox
          </Button>

          {/* More Actions (3-dots, dropdown) */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem>
                Export Data
              </DropdownMenuItem>
              <DropdownMenuItem>
                Move to Folder
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <Flag className="h-4 w-4 mr-2" />
                Flag Deal
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Archive Deal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.div>
  )
}
