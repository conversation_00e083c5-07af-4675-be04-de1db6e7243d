"use client"

import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, ArrowRight, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Deal } from '@/lib/types/deal';
// Stage color utility moved inline
const getStageColor = (stage: string) => {
  const stageColors: Record<string, string> = {
    'Pre-Seed': 'bg-blue-100 text-blue-800',
    'Seed': 'bg-green-100 text-green-800',
    'Series A': 'bg-purple-100 text-purple-800',
    'Series B': 'bg-orange-100 text-orange-800',
    'Series C+': 'bg-red-100 text-red-800',
    'Growth': 'bg-indigo-100 text-indigo-800'
  };
  return stageColors[stage] || 'bg-gray-100 text-gray-800';
};

interface DealModalProps {
  deal: Deal | null;
  isOpen: boolean;
  onClose: () => void;
}

export function DealModal({ deal, isOpen, onClose }: DealModalProps) {
  if (!deal) return null;

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.95,
      y: 20
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: 20,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        delay: 0.1,
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const sparkleVariants = {
    hidden: { opacity: 0, rotate: -10, scale: 0.8 },
    visible: { 
      opacity: 1, 
      rotate: 0, 
      scale: 1,
      transition: {
        delay: 0.2,
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  // Extract sector as string
  const sectorDisplay = Array.isArray(deal.sector) 
    ? deal.sector.join(', ') 
    : deal.sector || 'Unknown';

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog open={isOpen} onOpenChange={onClose}>
          <DialogContent className="sm:max-w-md border-0 shadow-2xl">
            <motion.div
              variants={modalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="relative"
            >
              {/* Background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20 rounded-lg" />
              
              {/* Close button */}
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="absolute top-4 right-4 z-10 h-8 w-8 p-0 hover:bg-white/50"
              >
                <X className="h-4 w-4" />
              </Button>

              <div className="relative p-6 space-y-6">
                {/* Header with company info */}
                <motion.div variants={contentVariants} className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h2 className="text-xl font-bold text-foreground">
                        {deal.company_name || 'Unnamed Company'}
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        {sectorDisplay}
                      </p>
                    </div>
                    <Badge 
                      variant="secondary" 
                      className={cn(
                        "text-xs font-medium border",
                        getStageColor(deal.stage || '')
                      )}
                    >
                      {deal.stage || 'Unknown'}
                    </Badge>
                  </div>
                </motion.div>

                {/* Coming Soon Content */}
                <motion.div variants={contentVariants} className="text-center space-y-4">
                  <motion.div variants={sparkleVariants} className="flex justify-center">
                    <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                      <Star className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                  </motion.div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-foreground">
                      Deal Details Coming Soon
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      We're building something amazing! Full deal profiles, analytics, 
                      and collaboration tools are coming soon.
                    </p>
                  </div>

                  {/* Feature preview list */}
                  <motion.div 
                    variants={contentVariants}
                    className="bg-white/50 dark:bg-gray-900/50 rounded-lg p-4 space-y-2"
                  >
                    <h4 className="text-sm font-medium text-foreground mb-3">
                      What's Coming:
                    </h4>
                    <div className="space-y-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-3 w-3" />
                        <span>Detailed company profiles & financials</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-3 w-3" />
                        <span>AI-powered deal scoring & insights</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-3 w-3" />
                        <span>Team collaboration & notes</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-3 w-3" />
                        <span>Document management & due diligence</span>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Action buttons */}
                <motion.div variants={contentVariants} className="flex gap-3">
                  <Button 
                    onClick={onClose}
                    variant="outline" 
                    className="flex-1"
                  >
                    Back to Deals
                  </Button>
                  <Button 
                    onClick={onClose}
                    className="flex-1"
                  >
                    Got it!
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </DialogContent>
        </Dialog>
      )}
    </AnimatePresence>
  );
}
