"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import { Loader2, Lock, Mail, User, Save } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { AvatarUploader } from "./avatar-uploader"
import { ChangePasswordDialog } from "./change-password-dialog"
import { UploadsAPI } from "@/lib/api/uploads-api"
import { useAuth } from "@/lib/auth-context"

const profileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
})

type ProfileFormValues = z.infer<typeof profileSchema>

interface ProfileFormProps {
  user: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  onUpdate: (data: { name: string; avatar?: string }) => Promise<void>
}

export function ProfileForm({
  user,
  onUpdate,
}: ProfileFormProps) {
  const { token } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [currentAvatar, setCurrentAvatar] = useState(user.avatar)

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user.name,
      email: user.email,
    },
  })

  const handleSubmit = async (data: ProfileFormValues) => {
    setIsLoading(true)
    try {
      await onUpdate({
        name: data.name,
        avatar: currentAvatar,
      })
      
      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      })
    } catch (error) {
      console.error('Profile update error:', error)
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "Failed to update profile",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAvatarUpload = async (file: File) => {
    if (!token) throw new Error("No authentication token")
    const avatarUrl = await UploadsAPI.uploadAvatar(file, token)
    setCurrentAvatar(avatarUrl)
    return avatarUrl
  }

  const handleAvatarRemove = async () => {
    setCurrentAvatar(undefined)
    // TODO: Implement avatar removal API call if needed
  }

  const isDirty = form.formState.isDirty || currentAvatar !== user.avatar

  return (
    <div className="space-y-8">
      {/* Avatar Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Profile Picture
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Upload a profile picture to personalize your account
          </p>
        </div>
        
        <AvatarUploader
          currentAvatar={currentAvatar}
          userName={user.name}
          onUpload={handleAvatarUpload}
          onRemove={handleAvatarRemove}
          size="lg"
        />
      </div>

      <Separator className="bg-gray-200 dark:bg-gray-700" />

      {/* Profile Information */}
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Personal Information
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Update your personal details and contact information
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium flex items-center">
              <User className="w-4 h-4 mr-2" />
              Full Name
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              className="h-11 bg-white/50 dark:bg-black/50 border-white/20 dark:border-white/10 focus:border-blue-500/50 focus:ring-blue-500/20"
              {...form.register("name")}
            />
            {form.formState.errors.name && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.name.message}
              </motion.p>
            )}
          </div>

          {/* Email Field (Read-only) */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium flex items-center">
              <Mail className="w-4 h-4 mr-2" />
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              readOnly
              className="h-11 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-500 cursor-not-allowed"
              {...form.register("email")}
            />
            <p className="text-xs text-gray-500">
              Email address cannot be changed. Contact support if needed.
            </p>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center space-x-4">
            <Button
              type="submit"
              disabled={!isDirty || isLoading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium shadow-lg shadow-blue-500/25 transition-all duration-200"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>

            {isDirty && (
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-sm text-amber-600 dark:text-amber-400"
              >
                You have unsaved changes
              </motion.p>
            )}
          </div>
        </div>
      </form>

      <Separator className="bg-gray-200 dark:bg-gray-700" />

      {/* Security Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Security
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Manage your account security and password
          </p>
        </div>

        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <Lock className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">
                Password
              </p>
              <p className="text-sm text-gray-500">
                Last updated 30 days ago
              </p>
            </div>
          </div>
          
          <Button
            variant="outline"
            onClick={() => setShowPasswordDialog(true)}
            className="bg-white/70 dark:bg-black/70 backdrop-blur-sm"
          >
            Change Password
          </Button>
        </div>
      </div>

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        open={showPasswordDialog}
        onOpenChange={setShowPasswordDialog}
        onPasswordChange={async (currentPassword, newPassword) => {
          // TODO: Implement password change API call
          console.log('Password change:', { currentPassword, newPassword })
          toast({
            title: "Password updated",
            description: "Your password has been changed successfully.",
          })
        }}
      />
    </div>
  )
}
