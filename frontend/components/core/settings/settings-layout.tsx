"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { User, Building2, Users, Settings } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface SettingsLayoutProps {
  children?: React.ReactNode
  defaultTab?: string
  userRole?: "admin" | "member"
  className?: string
}

interface TabConfig {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  adminOnly?: boolean
}

const tabs: TabConfig[] = [
  {
    id: "profile",
    label: "Profile",
    icon: User,
    description: "Manage your personal account settings and preferences"
  },
  {
    id: "organization", 
    label: "Organization",
    icon: Building2,
    description: "Configure your organization's branding and settings",
    adminOnly: true
  },
  {
    id: "members",
    label: "Members", 
    icon: Users,
    description: "Manage team members and user permissions",
    adminOnly: true
  }
]

export function SettingsLayout({
  children,
  defaultTab = "profile",
  userRole = "member",
  className
}: SettingsLayoutProps) {
  const [activeTab, setActiveTab] = useState(defaultTab)

  const availableTabs = tabs.filter(tab => 
    !tab.adminOnly || userRole === "admin"
  )

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800", className)}>
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-100/30 to-transparent dark:from-transparent dark:via-slate-800/30 dark:to-transparent" />
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-50/20 to-transparent dark:from-transparent dark:via-slate-900/20 dark:to-transparent" />
      
      <div className="relative">
        {/* Header */}
        <div className="border-b border-white/20 dark:border-white/10 bg-white/70 dark:bg-black/70 backdrop-blur-2xl">
          <div className="max-w-screen-xl mx-auto px-8 py-8">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
                  Settings
                </h1>
                <p className="text-muted-foreground mt-1">
                  Manage your account, organization, and team preferences
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-screen-xl mx-auto px-8 py-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            {/* Tab Navigation */}
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 lg:grid-cols-3 bg-white/70 dark:bg-black/70 backdrop-blur-2xl border border-white/20 dark:border-white/10 p-2 rounded-2xl shadow-lg">
              {availableTabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="relative flex items-center space-x-3 px-6 py-4 rounded-xl transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg"
                  >
                    <Icon className="w-5 h-5" />
                    <div className="text-left hidden sm:block">
                      <div className="font-medium">{tab.label}</div>
                      <div className="text-xs opacity-70 hidden lg:block">
                        {tab.description}
                      </div>
                    </div>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {/* Tab Content */}
            <div className="space-y-8">
              {availableTabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-0">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="border-0 bg-white/70 dark:bg-black/70 backdrop-blur-2xl shadow-2xl shadow-black/5 dark:shadow-white/5">
                      <CardHeader className="pb-6">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <tab.icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-2xl font-semibold">
                              {tab.label}
                            </CardTitle>
                            <CardDescription className="text-base mt-1">
                              {tab.description}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-8">
                        {children}
                      </CardContent>
                    </Card>
                  </motion.div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Export individual tab content components for use in pages
export function ProfileTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function OrganizationTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function MembersTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}
