"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { MoreHorizontal, Mail, Trash2, UserCheck, UserX, Crown, User, Clock } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

export interface Member {
  id: string
  name: string
  email: string
  avatar?: string
  status: "INVITED" | "ACTIVE" | "SUSPENDED"
  role: "admin" | "member"
  lastLogin?: Date
  invitedAt?: Date
}

interface MembersTableProps {
  members: Member[]
  currentUserId: string
  isAdmin: boolean
  onResendInvite: (memberId: string) => Promise<void>
  onRemoveMember: (memberId: string) => Promise<void>
  onUpdateRole?: (memberId: string, role: "admin" | "member") => Promise<void>
  className?: string
}

export function MembersTable({
  members,
  currentUserId,
  isAdmin,
  onResendInvite,
  onRemoveMember,
  onUpdateRole,
  className
}: MembersTableProps) {
  const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({})

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusBadge = (status: Member["status"]) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <UserCheck className="w-3 h-3 mr-1" />
            Active
          </Badge>
        )
      case "INVITED":
        return (
          <Badge variant="secondary" className="bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400">
            <Clock className="w-3 h-3 mr-1" />
            Invited
          </Badge>
        )
      case "SUSPENDED":
        return (
          <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            <UserX className="w-3 h-3 mr-1" />
            Suspended
          </Badge>
        )
    }
  }

  const getRoleBadge = (role: Member["role"]) => {
    if (role === "admin") {
      return (
        <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
          <Crown className="w-3 h-3 mr-1" />
          Admin
        </Badge>
      )
    }
    return (
      <Badge variant="outline" className="text-gray-600 dark:text-gray-400">
        <User className="w-3 h-3 mr-1" />
        Member
      </Badge>
    )
  }

  const handleAction = async (action: () => Promise<void>, memberId: string, actionName: string) => {
    setLoadingActions(prev => ({ ...prev, [memberId]: true }))
    try {
      await action()
      toast({
        title: "Success",
        description: `${actionName} completed successfully.`,
      })
    } catch (error) {
      console.error(`${actionName} error:`, error)
      toast({
        title: "Error",
        description: `Failed to ${actionName.toLowerCase()}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setLoadingActions(prev => ({ ...prev, [memberId]: false }))
    }
  }

  const formatLastLogin = (date?: Date) => {
    if (!date) return "Never"
    return formatDistanceToNow(date, { addSuffix: true })
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="rounded-xl border border-white/20 dark:border-white/10 bg-white/70 dark:bg-black/70 backdrop-blur-2xl overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-white/20 dark:border-white/10 hover:bg-transparent">
              <TableHead className="font-semibold text-gray-900 dark:text-gray-100">Member</TableHead>
              <TableHead className="font-semibold text-gray-900 dark:text-gray-100">Status</TableHead>
              <TableHead className="font-semibold text-gray-900 dark:text-gray-100">Role</TableHead>
              <TableHead className="font-semibold text-gray-900 dark:text-gray-100">Last Login</TableHead>
              {isAdmin && (
                <TableHead className="font-semibold text-gray-900 dark:text-gray-100 text-right">Actions</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member, index) => (
              <motion.tr
                key={member.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="border-white/20 dark:border-white/10 hover:bg-white/50 dark:hover:bg-black/50 transition-colors"
              >
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-10 h-10 border-2 border-white dark:border-gray-800">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-sm">
                        {getInitials(member.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {member.name}
                        </p>
                        {member.id === currentUserId && (
                          <Badge variant="outline" className="text-xs">You</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{member.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(member.status)}
                </TableCell>
                <TableCell>
                  {getRoleBadge(member.role)}
                </TableCell>
                <TableCell>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {member.status === "INVITED" 
                      ? `Invited ${formatLastLogin(member.invitedAt)}`
                      : formatLastLogin(member.lastLogin)
                    }
                  </span>
                </TableCell>
                {isAdmin && (
                  <TableCell className="text-right">
                    {member.id !== currentUserId && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            disabled={loadingActions[member.id]}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-white/95 dark:bg-black/95 backdrop-blur-2xl border-white/20 dark:border-white/10">
                          {member.status === "INVITED" && (
                            <DropdownMenuItem
                              onClick={() => handleAction(
                                () => onResendInvite(member.id),
                                member.id,
                                "Resend invite"
                              )}
                              className="cursor-pointer"
                            >
                              <Mail className="w-4 h-4 mr-2" />
                              Resend Invite
                            </DropdownMenuItem>
                          )}
                          
                          {onUpdateRole && member.status === "ACTIVE" && (
                            <>
                              <DropdownMenuItem
                                onClick={() => handleAction(
                                  () => onUpdateRole(member.id, member.role === "admin" ? "member" : "admin"),
                                  member.id,
                                  member.role === "admin" ? "Remove admin" : "Make admin"
                                )}
                                className="cursor-pointer"
                              >
                                {member.role === "admin" ? (
                                  <>
                                    <User className="w-4 h-4 mr-2" />
                                    Remove Admin
                                  </>
                                ) : (
                                  <>
                                    <Crown className="w-4 h-4 mr-2" />
                                    Make Admin
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                            </>
                          )}
                          
                          <DropdownMenuItem
                            onClick={() => handleAction(
                              () => onRemoveMember(member.id),
                              member.id,
                              "Remove member"
                            )}
                            className="cursor-pointer text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Remove Member
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                )}
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {members.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            No members yet
          </h3>
          <p className="text-gray-500">
            Invite team members to start collaborating on TractionX.
          </p>
        </div>
      )}
    </div>
  )
}
