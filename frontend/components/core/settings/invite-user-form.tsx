"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import { UserPlus, Mail, Crown, User, Loader2, Send } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"

const inviteSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.enum(["admin", "member"], {
    required_error: "Please select a role",
  }),
})

type InviteFormValues = z.infer<typeof inviteSchema>

interface InviteUserFormProps {
  onInvite: (email: string, role: "admin" | "member") => Promise<void>
  className?: string
}

export function InviteUserForm({ onInvite, className }: InviteUserFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<InviteFormValues>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: "",
      role: "member",
    },
  })

  const handleSubmit = async (data: InviteFormValues) => {
    setIsLoading(true)
    try {
      await onInvite(data.email, data.role)
      
      toast({
        title: "Invitation sent!",
        description: `An invitation has been sent to ${data.email}.`,
      })
      
      form.reset()
    } catch (error) {
      console.error('Invite error:', error)
      toast({
        title: "Invitation failed",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className={`border-0 bg-white/70 dark:bg-black/70 backdrop-blur-2xl shadow-lg ${className}`}>
      <CardHeader>
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
            <UserPlus className="w-5 h-5 text-white" />
          </div>
          <div>
            <CardTitle className="text-xl font-semibold">
              Invite Team Member
            </CardTitle>
            <CardDescription>
              Send an invitation to join your organization on TractionX
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Email Field */}
            <div className="md:col-span-2 space-y-2">
              <Label htmlFor="email" className="text-sm font-medium flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="h-11 bg-white/50 dark:bg-black/50 border-white/20 dark:border-white/10 focus:border-blue-500/50 focus:ring-blue-500/20"
                {...form.register("email")}
              />
              {form.formState.errors.email && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-red-500"
                >
                  {form.formState.errors.email.message}
                </motion.p>
              )}
            </div>

            {/* Role Field */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center">
                <Crown className="w-4 h-4 mr-2" />
                Role
              </Label>
              <Select
                value={form.watch("role")}
                onValueChange={(value: "admin" | "member") => form.setValue("role", value)}
              >
                <SelectTrigger className="h-11 bg-white/50 dark:bg-black/50 border-white/20 dark:border-white/10 focus:border-blue-500/50 focus:ring-blue-500/20">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent className="bg-white/95 dark:bg-black/95 backdrop-blur-2xl border-white/20 dark:border-white/10">
                  <SelectItem value="member" className="cursor-pointer">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-2" />
                      Member
                    </div>
                  </SelectItem>
                  <SelectItem value="admin" className="cursor-pointer">
                    <div className="flex items-center">
                      <Crown className="w-4 h-4 mr-2" />
                      Admin
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.role && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-red-500"
                >
                  {form.formState.errors.role.message}
                </motion.p>
              )}
            </div>
          </div>

          {/* Role Descriptions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-2 mb-2">
                <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <h4 className="font-medium text-blue-900 dark:text-blue-100">Member</h4>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Can view and manage deals, forms, and submissions. Cannot manage organization settings or invite users.
              </p>
            </div>

            <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
              <div className="flex items-center space-x-2 mb-2">
                <Crown className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                <h4 className="font-medium text-purple-900 dark:text-purple-100">Admin</h4>
              </div>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                Full access to all features including organization settings, user management, and billing.
              </p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium shadow-lg shadow-green-500/25 transition-all duration-200"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Help Text */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
            What happens next?
          </h4>
          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <li>• The invitee will receive a branded email with an invitation link</li>
            <li>• They can accept the invitation and set up their account</li>
            <li>• Once accepted, they'll appear in your members list</li>
            <li>• You can manage their role and permissions anytime</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
