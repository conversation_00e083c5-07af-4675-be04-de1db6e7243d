"use client"

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Star, 
  Target, 
  FileText, 
  Share2,
  ArrowRight,
  CheckCircle,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
// Onboarding steps defined inline
const onboardingSteps = [
  {
    id: 1,
    title: 'Create Your First Form',
    description: 'Set up a submission form to start collecting startup applications',
    completed: false,
    action: 'Create Form',
    icon: 'FileText',
    href: '/forms/new'
  },
  {
    id: 2, 
    title: 'Build Investment Thesis',
    description: 'Define your investment criteria and scoring rules',
    completed: false,
    action: 'Build Thesis',
    icon: 'Target',
    href: '/thesis/new'
  },
  {
    id: 3,
    title: 'Share Your Form',
    description: 'Get your unique form link and start receiving submissions',
    completed: false,
    action: 'Get Link',
    icon: 'Share2',
    href: '/forms'
  }
];
import Link from 'next/link';

interface OnboardingFlowProps {
  className?: string;
  onDismiss?: () => void;
}

const iconMap = {
  welcome: Star,
  target: Target,
  form: FileText,
  share: Share2,
};

export function OnboardingFlow({ className, onDismiss }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  const handleStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId]);
    }
    
    // Move to next step if not the last one
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      onDismiss?.();
    }, 300);
  };

  const progress = (completedSteps.length / onboardingSteps.length) * 100;

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -20,
      transition: {
        duration: 0.3,
        ease: "easeIn"
      }
    }
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      x: -20,
      transition: {
        duration: 0.3,
        ease: "easeIn"
      }
    }
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className={cn("relative", className)}
      >
        <Card className="relative overflow-hidden border-2 border-slate-200 dark:border-slate-700">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950/20 dark:via-blue-950/20 dark:to-indigo-950/20" />
          
          {/* Dismiss button */}
          <Button
            onClick={handleDismiss}
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 z-10 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>

          <CardHeader className="relative pb-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
                <Star className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-foreground">
                  Welcome to TractionX
                </CardTitle>
                <p className="text-base text-muted-foreground mt-1">
                  Let's get you started with your investment workflow
                </p>
              </div>
            </div>

            {/* Progress bar */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-base font-semibold text-foreground">
                  Progress
                </span>
                <span className="text-base text-muted-foreground">
                  {completedSteps.length} of {onboardingSteps.length} completed
                </span>
              </div>
              <Progress value={progress} className="h-3" />
            </div>
          </CardHeader>

          <CardContent className="relative space-y-6">
            {/* Steps */}
            <div className="space-y-4">
              {onboardingSteps.map((step, index) => {
                const IconComponent = iconMap[step.icon as keyof typeof iconMap] || Star;
                const isCompleted = completedSteps.includes(step.id);
                const isCurrent = index === currentStep;
                const isAccessible = index <= currentStep || isCompleted;

                return (
                  <AnimatePresence key={step.id}>
                    <motion.div
                      variants={stepVariants}
                      initial="hidden"
                      animate="visible"
                      className={cn(
                        "flex items-center gap-6 p-6 rounded-xl border transition-all duration-200",
                        isCompleted && "bg-emerald-50 dark:bg-emerald-950/20 border-emerald-200 dark:border-emerald-800",
                        isCurrent && !isCompleted && "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800",
                        !isAccessible && "opacity-50"
                      )}
                    >
                      {/* Step icon */}
                      <div className={cn(
                        "flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center",
                        isCompleted && "bg-emerald-100 dark:bg-emerald-900",
                        isCurrent && !isCompleted && "bg-blue-100 dark:bg-blue-900",
                        !isAccessible && "bg-muted"
                      )}>
                        {isCompleted ? (
                          <CheckCircle className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
                        ) : (
                          <IconComponent className={cn(
                            "h-6 w-6",
                            isCurrent && "text-blue-600 dark:text-blue-400",
                            !isAccessible && "text-muted-foreground"
                          )} />
                        )}
                      </div>

                      {/* Step content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className={cn(
                            "font-semibold text-lg",
                            isCompleted && "text-emerald-700 dark:text-emerald-300",
                            isCurrent && !isCompleted && "text-blue-700 dark:text-blue-300"
                          )}>
                            {step.title}
                          </h4>
                          {isCurrent && !isCompleted && (
                            <Badge variant="secondary" className="text-sm px-3 py-1">
                              Current
                            </Badge>
                          )}
                          {isCompleted && (
                            <Badge variant="secondary" className="text-sm px-3 py-1 bg-emerald-100 text-emerald-700">
                              Complete
                            </Badge>
                          )}
                        </div>
                        <p className="text-base text-muted-foreground">
                          {step.description}
                        </p>
                      </div>

                      {/* Action button */}
                      {step.action && step.href && isAccessible && !isCompleted && (
                        <div className="flex-shrink-0">
                          <Link href={step.href}>
                            <Button
                              size="sm"
                              variant={isCurrent ? "default" : "outline"}
                              onClick={() => handleStepComplete(step.id)}
                              className="gap-2"
                            >
                              {step.action}
                              <ArrowRight className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      )}
                    </motion.div>
                  </AnimatePresence>
                );
              })}
            </div>

            {/* Completion message */}
            {completedSteps.length === onboardingSteps.length && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-4 bg-emerald-50 dark:bg-emerald-950/20 rounded-lg border border-emerald-200 dark:border-emerald-800"
              >
                <CheckCircle className="h-8 w-8 text-emerald-600 dark:text-emerald-400 mx-auto mb-2" />
                <h4 className="font-medium text-emerald-700 dark:text-emerald-300 mb-1">
                  Setup Complete!
                </h4>
                <p className="text-sm text-emerald-600 dark:text-emerald-400">
                  You're all set to start using TractionX for your investment workflow.
                </p>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
