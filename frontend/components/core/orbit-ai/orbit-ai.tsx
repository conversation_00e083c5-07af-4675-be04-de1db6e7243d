"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Send,
  Minus,
  GripVertical,
  Loader2,
  ExternalLink,
  AlertCircle,
  RefreshCw
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { ChatAPI, ChatMessage, ChatThread, ChatSource, ChatMode } from "@/lib/api/chat-api"
import { DealDetailData } from "@/lib/types/deal-detail"
import { OrbitIcon } from "./orbit-icon"
import { Loading, LoadingDots } from "@/components/ui/loading"

interface OrbitAIProps {
  dealId: string
  dealContext?: DealDetailData | null
  className?: string
}

const getContextualPrompts = (dealContext: any, mode: ChatMode) => {
  if (mode === 'chat') {
    // Chat mode - conversational, strategic prompts
    const basePrompts = [
      "Summarize this deal",
      "What are the key risks?",
      "How does this score compare?",
      "Should I invest?"
    ]

    if (dealContext?.score_breakdown) {
      return [
        `Explain the ${dealContext.score_breakdown.overall_score} score`,
        "What drives the team strength rating?",
        "How strong is the team?",
        "What's the market opportunity?"
      ]
    }

    return basePrompts
  } else if (mode === 'research') {
    // Research mode - data-driven, sourced prompts
    const basePrompts = [
      "Show me market analysis with sources",
      "Find recent funding rounds in this sector",
      "Research the competitive landscape",
      "Get latest news about this company"
    ]

    if (dealContext?.score_breakdown) {
      return [
        "Research competitors with funding data",
        "Find recent funding rounds in this sector",
        "Show market size data with sources",
        "Get latest news about this company"
      ]
    }

    return basePrompts
  } else {
    // Agent mode - autonomous analysis prompts
    return [
      "Run full deal analysis",
      "Identify all risks automatically",
      "Compare to similar deals",
      "Generate investment memo"
    ]
  }
}

export function OrbitAI({ dealId, dealContext, className }: OrbitAIProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)
  const [activeMode, setActiveMode] = useState<ChatMode>('chat')
  const [thread, setThread] = useState<ChatThread | null>(null)
  const [error, setError] = useState<string | null>(null)
  const dragRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  // Load chat history
  const loadChatHistory = useCallback(async () => {
    if (!dealId) return

    try {
      setIsLoading(true)
      setError(null)

      const response = await ChatAPI.getChatHistory(dealId, activeMode)
      setThread(response.thread)
      setMessages(response.thread.messages)

    } catch (error) {
      console.error('Error loading chat history:', error)
      setError('Failed to load chat history')
    } finally {
      setIsLoading(false)
    }
  }, [dealId, activeMode])

  // Load chat history when component mounts, dealId changes, or mode changes
  useEffect(() => {
    if (dealId && isExpanded) {
      loadChatHistory()
    }
  }, [dealId, isExpanded, activeMode, loadChatHistory])

  // Initialize position (bottom right)
  useEffect(() => {
    const updatePosition = () => {
      const savedPosition = sessionStorage.getItem('orbit-position')
      if (savedPosition) {
        try {
          const parsed = JSON.parse(savedPosition)
          // Validate position is within viewport bounds
          const maxX = window.innerWidth - (isExpanded ? 380 : 200)
          const maxY = window.innerHeight - (isExpanded ? 620 : 80)

          if (parsed.x >= 0 && parsed.x <= maxX && parsed.y >= 0 && parsed.y <= maxY) {
            setPosition(parsed)
            return
          }
        } catch (e) {
          console.warn('Invalid saved position, using default')
        }
      }

             // Default to bottom right with safe margins
       const safeMargin = 20
                 const newPosition = {
          x: Math.max(safeMargin, window.innerWidth - (isExpanded ? 380 : 200) - safeMargin),
          y: Math.max(safeMargin, window.innerHeight - (isExpanded ? 620 : 80) - safeMargin)
        }
        setPosition(newPosition)
    }

    updatePosition()
    window.addEventListener('resize', updatePosition)
    return () => window.removeEventListener('resize', updatePosition)
  }, [isExpanded])



  // Show tooltip on first open
  useEffect(() => {
    if (isExpanded && messages.length === 0) {
      setShowTooltip(true)
      const timer = setTimeout(() => setShowTooltip(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [isExpanded, messages.length])

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Drag functionality
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return
      
      const newX = Math.max(0, Math.min(window.innerWidth - (isExpanded ? 360 : 200), e.clientX - 100))
      const newY = Math.max(0, Math.min(window.innerHeight - (isExpanded ? 600 : 60), e.clientY - 30))
      
      setPosition({ x: newX, y: newY })
    }

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)
        sessionStorage.setItem('orbit-position', JSON.stringify(position))
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, position, isExpanded])

  // Send message with real API integration (synchronous)
  const handleSendMessage = async (content: string) => {
    if (!content.trim() || !dealId || isLoading) return

    try {
      setInputValue("")
      setIsLoading(true)
      setError(null)

      // Send message and get immediate AI response
      const aiMessage = await ChatAPI.sendMessage(dealId, {
        message: content.trim(),
        mode: activeMode,
        agent_type: "investment_analysis",
        include_deal_context: true
      })

      // Reload chat history to get both user message and AI response
      await loadChatHistory()

    } catch (error: any) {
      console.error('Error sending message:', error)

      // Handle different error types
      let errorMessage = 'Failed to send message'
      if (error?.response?.status === 429) {
        errorMessage = 'Rate limit exceeded. Please try again later.'
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again.'
      } else if (error?.response?.data?.detail) {
        errorMessage = error.response.data.detail
      }

      setError(errorMessage)
      toast({
        title: "Message Failed",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Retry failed message
  const retryMessage = useCallback(async () => {
    if (!thread?.messages.length) return

    const lastUserMessage = [...thread.messages]
      .reverse()
      .find(msg => msg.role === 'user')

    if (lastUserMessage) {
      await handleSendMessage(lastUserMessage.content)
    }
  }, [thread, handleSendMessage])

  const handleQuickPrompt = (prompt: string) => {
    handleSendMessage(prompt)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(inputValue)
    }
  }

  return (
    <motion.div
        ref={dragRef}
        className={cn("fixed z-[9999] select-none", className)}
        style={{
          left: position.x,
          top: position.y,
        }}
        animate={{
          scale: isDragging ? 1.05 : 1,
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <AnimatePresence mode="wait">
          {!isExpanded ? (
            // Collapsed State - Futuristic Chat Bubble
            <motion.div
              key="collapsed"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className={cn(
                "group cursor-pointer",
                "backdrop-blur-xl bg-white/70 dark:bg-black/70",
                "border-2 border-primary/50",
                "rounded-2xl shadow-2xl",
                "hover:shadow-primary/30 hover:shadow-2xl hover:border-primary/80",
                "transition-all duration-300 ease-out",
                "hover:bg-white/80 dark:hover:bg-black/80",
                isDragging && "shadow-primary/40 shadow-2xl"
              )}
              onClick={() => setIsExpanded(true)}
              onMouseDown={() => setIsDragging(true)}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 20px rgba(56, 189, 248, 0.3)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="flex items-center gap-3 px-4 py-3">
                <OrbitIcon 
                  className="h-6 w-6" 
                  animated={true} 
                  glowing={true} 
                />
                <span className="font-semibold text-foreground">
                  Ask Orbit
                </span>
              </div>
            </motion.div>
        ) : (
          // Expanded State - Futuristic Chat Panel
          <motion.div
            key="expanded"
            initial={{
              opacity: 0,
              scale: 0.8,
              x: 0,
              y: 0
            }}
            animate={{
              opacity: 1,
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{
              opacity: 0,
              scale: 0.8,
              x: 0,
              y: 0
            }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 25,
              duration: 0.4
            }}
            className={cn(
              "w-[360px] h-[600px] max-h-[80vh]",
              "backdrop-blur-2xl bg-white/70 dark:bg-black/70",
              "border-2 border-primary/50",
              "rounded-2xl shadow-2xl",
              "flex flex-col overflow-hidden",
              "font-['Space_Grotesk',sans-serif]",
              isDragging && "shadow-primary/40 shadow-2xl"
            )}
            style={{
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 30px rgba(56, 189, 248, 0.1)"
            }}
          >
            {/* Futuristic Header with Mode Toggle */}
            <div
              className={cn(
                "flex items-center justify-between p-4",
                "border-b border-primary/20",
                "cursor-grab active:cursor-grabbing",
                "bg-gradient-to-r from-transparent via-primary/5 to-transparent",
                isDragging && "cursor-grabbing"
              )}
              onMouseDown={() => setIsDragging(true)}
            >
              <div className="flex items-center gap-3">
                <OrbitIcon className="h-5 w-5" animated={true} />
                <span className="font-semibold text-foreground text-lg">Orbit AI</span>
                <Badge
                  variant="secondary"
                  className={cn(
                    "text-xs px-2 py-0.5 rounded-full border backdrop-blur-xl",
                    activeMode === 'chat' && "bg-blue-100/50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800",
                    activeMode === 'research' && "bg-purple-100/50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800",
                    activeMode === 'agent' && "bg-green-100/50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800"
                  )}
                >
                  {activeMode === 'chat' && 'Chat'}
                  {activeMode === 'research' && 'Research'}
                  {activeMode === 'agent' && 'Agent'}
                </Badge>
              </div>

              {/* Mode Toggle */}
              <div className="flex items-center gap-3">
                <div className="flex items-center bg-white/10 dark:bg-black/10 rounded-full p-1 backdrop-blur-xl border border-primary/20">
                  <button
                    className={cn(
                      "px-2 py-1 text-xs font-medium rounded-full transition-all duration-200",
                      activeMode === 'chat'
                        ? "bg-primary text-primary-foreground shadow-lg"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                    onClick={() => {
                      setActiveMode('chat')
                      setMessages([])
                      setError(null)
                    }}
                    title="Chat Mode - Fast, human-like responses"
                  >
                    Chat
                  </button>
                  <button
                    className={cn(
                      "px-2 py-1 text-xs font-medium rounded-full transition-all duration-200",
                      activeMode === 'research'
                        ? "bg-primary text-primary-foreground shadow-lg"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                    onClick={() => {
                      setActiveMode('research')
                      setMessages([])
                      setError(null)
                    }}
                    title="Deep Research - Analyst-grade with sources"
                  >
                    Research
                  </button>
                  <button
                    className={cn(
                      "px-2 py-1 text-xs font-medium rounded-full transition-all duration-200 opacity-50 cursor-not-allowed",
                      "text-muted-foreground"
                    )}
                    disabled
                    title="Agent Mode – Coming Soon"
                  >
                    Agent
                  </button>
                </div>

                <div className="flex items-center gap-1">
                  <GripVertical className="h-4 w-4 text-muted-foreground/50" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="h-8 w-8 p-0 hover:bg-primary/10 rounded-full transition-all duration-200"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Futuristic Chat Area */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {/* Loading state */}
                {isLoading && messages.length === 0 && (
                  <div className="text-center py-8">
                    <div className="mx-auto mb-4">
                      <Loading size="lg" className="text-primary" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Loading chat history...
                    </p>
                  </div>
                )}

                {/* Error state */}
                {error && !isLoading && (
                  <div className="text-center py-8">
                    <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
                    <p className="text-sm text-red-500 mb-2">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={loadChatHistory}
                      className="text-xs"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Retry
                    </Button>
                  </div>
                )}

                {/* Empty state */}
                {!isLoading && !error && messages.length === 0 && (
                  <div className="text-center py-8">
                    <div className="mx-auto mb-4">
                      <OrbitIcon 
                        className="h-12 w-12" 
                        animated={true} 
                        glowing={true} 
                      />
                    </div>
                    <p className="text-base font-medium text-foreground mb-2">
                      Hi! I'm Orbit, your AI investment assistant.
                    </p>
                    <p className="text-sm text-muted-foreground mb-2">
                      {activeMode === 'chat' && "Ask me anything about this deal. I'll provide fast, strategic insights."}
                      {activeMode === 'research' && "Ask me for research with sources. I'll provide analyst-grade analysis."}
                      {activeMode === 'agent' && "I'll run autonomous analysis and provide proactive insights."}
                    </p>
                    <div className="flex items-center justify-center gap-2">
                      <Badge
                        variant="secondary"
                        className={cn(
                          "text-xs px-3 py-1 rounded-full",
                          activeMode === 'chat' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                          activeMode === 'research' && "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
                          activeMode === 'agent' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                        )}
                      >
                        {activeMode === 'chat' && '💬 Chat Mode'}
                        {activeMode === 'research' && '🔍 Research Mode'}
                        {activeMode === 'agent' && '🤖 Agent Mode'}
                      </Badge>
                    </div>
                  </div>
                )}

                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                    className={cn(
                      "flex",
                      message.role === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] px-4 py-3 rounded-2xl",
                        "backdrop-blur-xl border",
                        message.role === 'user'
                          ? "bg-primary/20 border-primary/30 text-foreground ml-4 shadow-lg shadow-primary/10"
                          : "bg-white/40 dark:bg-black/40 border-white/20 dark:border-black/20 text-foreground mr-4"
                      )}
                    >
                      <div className="space-y-2">
                        {/* Mode indicator for AI messages */}
                        {message.role === 'assistant' && (
                          <div className="flex items-center gap-2 mb-2">
                            <Badge
                              variant="secondary"
                              className={cn(
                                "text-xs px-2 py-0.5 rounded-full",
                                message.mode === 'chat' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                                message.mode === 'research' && "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
                                message.mode === 'agent' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                              )}
                            >
                              {message.mode === 'chat' && '💬 Chat'}
                              {message.mode === 'research' && '🔍 Research'}
                              {message.mode === 'agent' && '🤖 Agent'}
                            </Badge>
                            {message.ai_model && (
                              <span className="text-xs text-muted-foreground">
                                {message.ai_model}
                              </span>
                            )}
                            {message.response_time_ms && (
                              <span className="text-xs text-muted-foreground">
                                {message.response_time_ms}ms
                              </span>
                            )}
                          </div>
                        )}

                        <p className="text-sm leading-relaxed">{message.content}</p>

                        {/* Status indicator for failed messages */}
                        {message.status === 'failed' && (
                          <div className="flex items-center gap-2 text-xs text-red-500">
                            <AlertCircle className="h-3 w-3" />
                            <span>Failed to generate response</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={retryMessage}
                              className="h-5 px-2 text-xs"
                            >
                              <RefreshCw className="h-3 w-3 mr-1" />
                              Retry
                            </Button>
                          </div>
                        )}

                        {/* Sources for AI messages (mainly Research mode) */}
                        {message.role === 'assistant' && message.sources && message.sources.length > 0 && (
                          <div className="space-y-1">
                            <p className="text-xs font-medium text-muted-foreground">Sources:</p>
                            <div className="space-y-1">
                              {message.sources.map((source, index) => (
                                <a
                                  key={index}
                                  href={source.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center gap-2 text-xs text-primary hover:text-primary/80 transition-colors"
                                >
                                  <ExternalLink className="h-3 w-3" />
                                  <span className="truncate">{source.title}</span>
                                </a>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}

                {isLoading && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex justify-start"
                  >
                    <div className="bg-white/40 dark:bg-black/40 backdrop-blur-xl px-4 py-3 rounded-2xl mr-4 border border-white/20 dark:border-black/20">
                      <div className="flex items-center gap-3">
                        <LoadingDots className="text-primary" />
                        <span className="text-sm text-muted-foreground">Orbit is thinking...</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Futuristic Quick Prompts */}
            {messages.length === 0 && (
              <div className="px-4 pb-3">
                <div className="flex flex-wrap gap-2">
                  {getContextualPrompts(dealContext, activeMode).map((prompt, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleQuickPrompt(prompt)}
                        className={cn(
                          "text-xs font-medium",
                          "bg-white/20 dark:bg-black/20 backdrop-blur-xl",
                          "hover:bg-primary/20 hover:border-primary/40",
                          "border border-white/20 dark:border-black/20",
                          "rounded-full transition-all duration-300",
                          "hover:scale-105 hover:shadow-lg hover:shadow-primary/10"
                        )}
                      >
                        {prompt}
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Futuristic Input Bar */}
            <div className="p-4 border-t border-primary/20 bg-gradient-to-r from-transparent via-primary/5 to-transparent">
              <div className="flex gap-3">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask Orbit..."
                  className={cn(
                    "flex-1 bg-white/30 dark:bg-black/30 backdrop-blur-xl",
                    "border-white/30 dark:border-black/30",
                    "rounded-2xl px-4 py-3",
                    "placeholder:text-muted-foreground/70",
                    "focus:border-primary/50 focus:ring-2 focus:ring-primary/20",
                    "transition-all duration-300"
                  )}
                  disabled={isLoading}
                />
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => handleSendMessage(inputValue)}
                    disabled={!inputValue.trim() || isLoading}
                    className={cn(
                      "px-4 py-3 rounded-2xl",
                      "bg-primary hover:bg-primary/90",
                      "shadow-lg transition-all duration-300",
                      inputValue.trim() && "shadow-primary/30 shadow-xl"
                    )}
                  >
                    {isLoading ? (
                      <LoadingDots className="h-4 w-4" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </motion.div>
              </div>

              {/* Futuristic Tooltip */}
              <AnimatePresence>
                {showTooltip && (
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.9 }}
                    className="text-center mt-3"
                  >
                    <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-primary/10 border border-primary/20 backdrop-blur-xl">
                      <OrbitIcon className="h-3 w-3" animated={true} />
                      <p className="text-xs text-primary font-medium">
                        Drag anywhere to reposition
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
        </AnimatePresence>
      </motion.div>
  )
}
