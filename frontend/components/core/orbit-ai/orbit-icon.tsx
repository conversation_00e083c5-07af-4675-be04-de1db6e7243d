"use client"

import { useEffect, useRef } from "react"
import { animate } from "animejs"
import { cn } from "@/lib/utils"

interface OrbitIconProps {
  className?: string
  animated?: boolean
  glowing?: boolean
}

export function OrbitIcon({ className, animated = false, glowing = false }: OrbitIconProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  
  useEffect(() => {
    if (!animated || !svgRef.current) return

    // Animate SVG filters for distortion effect
    const filterAnimation = animate(['feTurbulence', 'feDisplacementMap'], {
      baseFrequency: 0.05,
      scale: 15,
      alternate: true,
      loop: true,
      duration: 3000,
      direction: 'alternate',
      easing: 'easeInOutSine'
    })

    // Animate polygon points for morphing effect
    const polygonAnimation = animate('polygon', {
      points: '64 68.64 8.574 100 63.446 67.68 64 4 64.554 67.68 119.426 100',
      alternate: true,
      loop: true,
      duration: 4000,
      direction: 'alternate',
      easing: 'easeInOutQuad'
    })

    // Core pulse animation
    const coreAnimation = animate('.orbit-core', {
      scale: [1, 1.2, 1],
      opacity: [0.6, 1, 0.6],
      duration: 2000,
      loop: true,
      direction: 'alternate',
      easing: 'easeInOutSine'
    })

    // Ring rotation animations
    const ring1Animation = animate('.orbit-ring-1', {
      rotate: '360deg',
      duration: 8000,
      loop: true,
      easing: 'linear'
    })

    const ring2Animation = animate('.orbit-ring-2', {
      rotate: '-360deg',
      duration: 12000,
      loop: true,
      easing: 'linear'
    })

    // Cleanup function
    return () => {
      filterAnimation.pause()
      polygonAnimation.pause()
      coreAnimation.pause()
      ring1Animation.pause()
      ring2Animation.pause()
    }
  }, [animated])

  return (
    <div className={cn("relative", className)}>
      <svg
        ref={svgRef}
        width="24"
        height="24"
        viewBox="0 0 128 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          "text-foreground/80",
          glowing && "drop-shadow-[0_0_8px_currentColor]"
        )}
      >
        {/* SVG Filters for distortion effects */}
        <defs>
          <filter id="turbulence" x="0%" y="0%" width="100%" height="100%">
            <feTurbulence
              baseFrequency="0.02"
              numOctaves="3"
              result="noise"
              seed="2"
            />
            <feDisplacementMap
              in="SourceGraphic"
              in2="noise"
              scale="8"
            />
          </filter>
          
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* Animated background polygon */}
        <polygon
          points="64 68.64 8.574 100 63.446 67.68 64 4 64.554 67.68 119.426 100"
          fill="none"
          stroke="currentColor"
          strokeWidth="0.5"
          opacity="0.2"
          filter={animated ? "url(#turbulence)" : undefined}
        />

        {/* Outer ring */}
        <circle
          className="orbit-ring-2"
          cx="64"
          cy="64"
          r="45"
          stroke="currentColor"
          strokeWidth="0.8"
          fill="none"
          opacity="0.3"
          strokeDasharray="2 4"
          style={{ transformOrigin: "64px 64px" }}
        />
        
        {/* Middle ring */}
        <circle
          className="orbit-ring-1"
          cx="64"
          cy="64"
          r="32"
          stroke="currentColor"
          strokeWidth="1"
          fill="none"
          opacity="0.5"
          strokeDasharray="3 3"
          style={{ transformOrigin: "64px 64px" }}
        />
        
        {/* Inner ring */}
        <circle
          cx="64"
          cy="64"
          r="20"
          stroke="currentColor"
          strokeWidth="1.2"
          fill="none"
          opacity="0.6"
        />

        {/* Central core */}
        <circle
          className="orbit-core"
          cx="64"
          cy="64"
          r="6"
          fill="currentColor"
          opacity="0.8"
          filter={glowing ? "url(#glow)" : undefined}
        />
        
        {/* Orbital dots */}
        <circle
          cx="96"
          cy="64"
          r="2"
          fill="currentColor"
          opacity="0.7"
          className="orbit-ring-1"
          style={{ transformOrigin: "64px 64px" }}
        />
        
        <circle
          cx="64"
          cy="19"
          r="1.5"
          fill="currentColor"
          opacity="0.5"
          className="orbit-ring-2"
          style={{ transformOrigin: "64px 64px" }}
        />
        
        <circle
          cx="32"
          cy="64"
          r="1"
          fill="currentColor"
          opacity="0.4"
          className="orbit-ring-1"
          style={{ transformOrigin: "64px 64px" }}
        />

        {/* Energy lines */}
        <g opacity="0.3">
          <line x1="64" y1="10" x2="64" y2="25" stroke="currentColor" strokeWidth="0.5" />
          <line x1="64" y1="103" x2="64" y2="118" stroke="currentColor" strokeWidth="0.5" />
          <line x1="10" y1="64" x2="25" y2="64" stroke="currentColor" strokeWidth="0.5" />
          <line x1="103" y1="64" x2="118" y2="64" stroke="currentColor" strokeWidth="0.5" />
        </g>

        {/* Corner accents */}
        <g opacity="0.2">
          <circle cx="20" cy="20" r="1" fill="currentColor" />
          <circle cx="108" cy="20" r="1" fill="currentColor" />
          <circle cx="20" cy="108" r="1" fill="currentColor" />
          <circle cx="108" cy="108" r="1" fill="currentColor" />
        </g>
      </svg>
      
      {/* Optional glow effect overlay */}
      {glowing && animated && (
        <div
          className="absolute inset-0 rounded-full bg-current opacity-10 blur-sm animate-pulse"
          style={{ 
            animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite' 
          }}
        />
      )}
    </div>
  )
}
