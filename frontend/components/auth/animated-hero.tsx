"use client"

import { useEffect, useRef, useState } from "react"
import { Icons } from "@/components/icons"
import { cn } from "@/lib/utils"
import { OrbitGridAnimation } from "./orbit-grid-animation"

// Types for anime.js to avoid TypeScript errors
declare global {
  interface Window {
    anime: any;
  }
}

// export function AnimatedHero() {
//   const heroRef = useRef<HTMLDivElement>(null)
//   const headlineRef = useRef<HTMLHeadingElement>(null)
//   const [mounted, setMounted] = useState(false)
//   const [animeLoaded, setAnimeLoaded] = useState(false)
//
//   // Load anime.js dynamically
//   useEffect(() => {
//     const loadAnime = async () => {
//       try {
//         const anime = await import('animejs')
//         window.anime = (anime as any).default || anime
//         setAnimeLoaded(true)
//       } catch (error) {
//         console.warn('Failed to load anime.js:', error)
//         // Fallback: still show component without animations
//         setAnimeLoaded(false)
//         setMounted(true)
//       }
//     }
//     loadAnime()
//     setMounted(true)
//   }, [])
//
//   // Hero headline shimmer animation
//   useEffect(() => {
//     if (!mounted || !animeLoaded || !window.anime || !headlineRef.current) return
//
//     const shimmerAnimation = () => {
//       // Create shimmer effect using CSS mask
//       const headline = headlineRef.current
//       if (!headline) return
//
//       headline.style.setProperty('--shimmer-position', '-100%')
//
//       window.anime({
//         targets: headline,
//         '--shimmer-position': '200%',
//         duration: 2000,
//         easing: 'easeInOutQuad',
//         complete: () => {
//           setTimeout(shimmerAnimation, 6000) // Repeat every 6 seconds
//         }
//       })
//     }
//
//     const timeoutId = setTimeout(shimmerAnimation, 1000) // Start after initial load
//     return () => clearTimeout(timeoutId)
//   }, [mounted, animeLoaded])
//
//   // Background grid animation
//   useEffect(() => {
//     if (!mounted || !animeLoaded || !window.anime) return
//
//     const animation = window.anime({
//       targets: '.grid-dot',
//       scale: [0.5, 1.2, 0.8],
//       opacity: [0.02, 0.08, 0.04],
//       delay: window.anime.stagger(200, { grid: [12, 8], from: 'center' }),
//       duration: 4000,
//       loop: true,
//       direction: 'alternate',
//       easing: 'easeInOutSine'
//     })
//
//     return () => {
//       if (animation && animation.pause) {
//         animation.pause()
//       }
//     }
//   }, [mounted, animeLoaded])
//
//   if (!mounted) return null
//
//   return (
//     <div ref={heroRef} className="relative z-10 text-center space-y-12 max-w-2xl">
//       {/* Background Grid */}
//       <div className="absolute inset-0 -z-10 overflow-hidden">
//         {Array.from({ length: 96 }).map((_, i) => (
//           <div
//             key={i}
//             className="grid-dot absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-4"
//             style={{
//               left: `${(i % 12) * 8.33}%`,
//               top: `${Math.floor(i / 12) * 12.5}%`,
//             }}
//           />
//         ))}
//       </div>
//
//       {/* Logo */}
//       <div className="flex justify-center mb-8">
//         <Icons.logoFull className="h-14 text-foreground" />
//       </div>
//
//       {/* Hero Headline with Shimmer */}
//       <div className="space-y-8">
//         <h1
//           ref={headlineRef}
//           className={cn(
//             "text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight",
//             "bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900",
//             "dark:from-slate-100 dark:via-slate-300 dark:to-slate-100",
//             "bg-clip-text text-transparent",
//             "relative overflow-hidden",
//             "px-4 sm:px-0"
//           )}
//           style={{
//             backgroundImage: `
//               linear-gradient(
//                 90deg,
//                 transparent 0%,
//                 transparent calc(var(--shimmer-position, -100%) - 10%),
//                 rgba(255,255,255,0.8) var(--shimmer-position, -100%),
//                 transparent calc(var(--shimmer-position, -100%) + 10%),
//                 transparent 100%
//               ),
//               linear-gradient(
//                 90deg,
//                 rgb(15, 23, 42),
//                 rgb(71, 85, 105),
//                 rgb(15, 23, 42)
//               )
//             `,
//             backgroundSize: '200% 100%, 100% 100%',
//             backgroundClip: 'text',
//             WebkitBackgroundClip: 'text',
//           }}
//         >
//           Welcome to the intelligence era of investing
//         </h1>
//
//         {/* Orbit Grid Animation */}
//         <OrbitGridAnimation className="py-8" />
//
//         {/* Subtitle */}
//         <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed max-w-lg mx-auto px-4 sm:px-0">
//           TractionX empowers investors with AI-driven insights, automated deal flow, and intelligent thesis matching.
//         </p>
//       </div>
//
//       {/* Agentic CTA Button */}
//       <div className="pt-8">
//         <button
//           className={cn(
//             "group relative px-4 sm:px-8 py-3 sm:py-4 rounded-full",
//             "bg-gradient-to-r from-slate-900 to-slate-800",
//             "dark:from-slate-100 dark:to-slate-200",
//             "border border-amber-400/30 hover:border-amber-400/60",
//             "font-mono text-xs sm:text-sm font-medium tracking-wide",
//             "text-amber-100 dark:text-slate-900",
//             "transition-all duration-300",
//             "hover:shadow-lg hover:shadow-amber-400/20",
//             "hover:scale-105 active:scale-95",
//             "overflow-hidden",
//             "w-full max-w-sm sm:max-w-none sm:w-auto"
//           )}
//           onClick={() => {
//             // Scroll to form or navigate as needed
//             document.querySelector('form')?.scrollIntoView({ behavior: 'smooth' })
//           }}
//         >
//           {/* Button shimmer effect */}
//           <div className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 group-hover:animate-[shimmer_1.5s_ease-in-out]" />
//
//           <span className="relative z-10 flex items-center justify-center space-x-2">
//             <span className="hidden sm:inline">WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS</span>
//             <span className="sm:hidden">AGENTIC OS FOR PRIVATE MARKETS</span>
//             <span className="text-amber-400">→</span>
//           </span>
//         </button>
//       </div>
//
//       <style jsx>{`
//         @keyframes shimmer {
//           0% { transform: translateX(-100%); }
//           100% { transform: translateX(100%); }
//         }
//
//         @keyframes fadeInUp {
//           from {
//             opacity: 0;
//             transform: translateY(20px);
//           }
//           to {
//             opacity: 1;
//             transform: translateY(0);
//           }
//         }
//       `}</style>
//     </div>
//   )
// }

export function AnimatedHero() {
  const heroRef = useRef<HTMLDivElement>(null)
  const headlineRef = useRef<HTMLHeadingElement>(null)
  const [mounted, setMounted] = useState(false)
  const [animeModule, setAnimeModule] = useState<any>(null)

  // Load anime.js dynamically
  useEffect(() => {
    const loadAnime = async () => {
      try {
        const anime = await import('animejs')
        setAnimeModule(anime.default)
      } catch (error) {
        console.warn('Failed to load anime.js:', error)
        setAnimeModule(null)
      }
    }
    loadAnime()
    setMounted(true)
  }, [])

  // Hero headline shimmer animation
  useEffect(() => {
    if (!mounted || !animeModule || !headlineRef.current) return

    const shimmerAnimation = () => {
      const headline = headlineRef.current
      if (!headline) return

      headline.style.setProperty('--shimmer-position', '-100%')

      animeModule({
        targets: headline,
        '--shimmer-position': '200%',
        duration: 2000,
        easing: 'easeInOutQuad',
        complete: () => {
          setTimeout(shimmerAnimation, 6000)
        }
      })
    }

    const timeoutId = setTimeout(shimmerAnimation, 1000)
    return () => clearTimeout(timeoutId)
  }, [mounted, animeModule])

  // Background grid animation
  useEffect(() => {
    if (!mounted || !animeModule) return

    const animation = animeModule({
      targets: '.grid-dot',
      scale: [0.5, 1.2, 0.8],
      opacity: [0.02, 0.08, 0.04],
      delay: animeModule.stagger(200, { grid: [12, 8], from: 'center' }),
      duration: 4000,
      loop: true,
      direction: 'alternate',
      easing: 'easeInOutSine'
    })

    return () => {
      if (animation && animation.pause) {
        animation.pause()
      }
    }
  }, [mounted, animeModule])

  if (!mounted) return null

  return (
    <div ref={heroRef} className="relative z-10 text-center space-y-12 max-w-2xl">
      {/* Background Grid */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {Array.from({ length: 96 }).map((_, i) => (
          <div
            key={i}
            className="grid-dot absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-4"
            style={{
              left: `${(i % 12) * 8.33}%`,
              top: `${Math.floor(i / 12) * 12.5}%`,
            }}
          />
        ))}
      </div>

      {/* Logo */}
      <div className="flex justify-center mb-8">
        <Icons.logoFull className="h-14 text-foreground" />
      </div>

      {/* Hero Headline with Shimmer */}
      <div className="space-y-8">
        <h1
          ref={headlineRef}
          className={cn(
            "text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight",
            "bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900",
            "dark:from-slate-100 dark:via-slate-300 dark:to-slate-100",
            "bg-clip-text text-transparent",
            "relative overflow-hidden",
            "px-4 sm:px-0"
          )}
          style={{
            backgroundImage: `
              linear-gradient(
                90deg,
                transparent 0%,
                transparent calc(var(--shimmer-position, -100%) - 10%),
                rgba(255,255,255,0.8) var(--shimmer-position, -100%),
                transparent calc(var(--shimmer-position, -100%) + 10%),
                transparent 100%
              ),
              linear-gradient(
                90deg,
                rgb(15, 23, 42),
                rgb(71, 85, 105),
                rgb(15, 23, 42)
              )
            `,
            backgroundSize: '200% 100%, 100% 100%',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
          }}
        >
          Welcome to the intelligence era of investing
        </h1>

        {/* Orbit Grid Animation */}
        <OrbitGridAnimation className="py-8" />

        {/* Subtitle */}
        <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed max-w-lg mx-auto px-4 sm:px-0">
          TractionX empowers investors with AI-driven insights, automated deal flow, and intelligent thesis matching.
        </p>
      </div>

      {/* Agentic CTA Button */}
      <div className="pt-8">
        <button
          className={cn(
            "group relative px-4 sm:px-8 py-3 sm:py-4 rounded-full",
            "bg-gradient-to-r from-slate-900 to-slate-800",
            "dark:from-slate-100 dark:to-slate-200",
            "border border-amber-400/30 hover:border-amber-400/60",
            "font-mono text-xs sm:text-sm font-medium tracking-wide",
            "text-amber-100 dark:text-slate-900",
            "transition-all duration-300",
            "hover:shadow-lg hover:shadow-amber-400/20",
            "hover:scale-105 active:scale-95",
            "overflow-hidden",
            "w-full max-w-sm sm:max-w-none sm:w-auto"
          )}
          onClick={() => {
            document.querySelector('form')?.scrollIntoView({ behavior: 'smooth' })
          }}
        >
          {/* Button shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 group-hover:animate-[shimmer_1.5s_ease-in-out]" />

          <span className="relative z-10 flex items-center justify-center space-x-2">
            <span className="hidden sm:inline">WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS</span>
            <span className="sm:hidden">AGENTIC OS FOR PRIVATE MARKETS</span>
            <span className="text-amber-400">→</span>
          </span>
        </button>
      </div>

      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  )
}
