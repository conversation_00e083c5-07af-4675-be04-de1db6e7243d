"use client"

import { motion } from "framer-motion"
import { Icons } from "@/components/icons"
import { cn } from "@/lib/utils"
import { AnimatedHero } from "@/components/auth/animated-hero"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showBackButton?: boolean
  backHref?: string
  className?: string
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showBackButton = false,
  backHref = "/",
  className,
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-200/20 to-transparent dark:via-slate-700/20" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-200/20 to-transparent dark:via-slate-700/20" />
      </div>
      
      <div className="relative flex min-h-screen">
        {/* Left Side - Animated Branding Hero */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center p-12 relative overflow-hidden">
          {/* Enhanced Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-pink-600/10" />
          <div className="absolute inset-0 bg-gradient-to-t from-slate-900/5 via-transparent to-slate-900/5 dark:from-slate-100/5 dark:to-slate-100/5" />
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="relative z-10 w-full flex items-center justify-center"
          >
            <AnimatedHero />
          </motion.div>
        </div>

        {/* Right Side - Auth Form */}
        <div className="flex-1 lg:w-1/2 flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className={cn("w-full max-w-md space-y-8", className)}
          >
            {/* Mobile Logo */}
            <div className="lg:hidden flex justify-center">
              <Icons.logoFull className="h-8 text-foreground" />
            </div>
            
            {/* Header */}
            <div className="text-center space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">{title}</h2>
              <p className="text-muted-foreground">{subtitle}</p>
            </div>

            {/* Form Content */}
            {children}
            
            {/* Footer */}
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                You're accessing TractionX Beta. 
                <br />
                Built with ❤️ for the future of investing.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
