"use client"

import {useEffect, useRef, useState} from "react"

// Types for anime.js
declare global {
  interface Window {
    anime: any;
  }
}

interface OrbitGridAnimationProps {
  className?: string
}

// export function OrbitGridAnimation({ className }: OrbitGridAnimationProps) {
//   const gridRef = useRef<HTMLDivElement>(null)
//   const [animeLoaded, setAnimeLoaded] = useState(false)
//   const [mounted, setMounted] = useState(false)
//
//   // Load anime.js dynamically
//   useEffect(() => {
//     const loadAnime = async () => {
//       try {
//         const anime = await import('animejs')
//         window.anime = (anime as any).default || anime
//         setAnimeLoaded(true)
//       } catch (error) {
//         console.warn('Failed to load anime.js:', error)
//       }
//     }
//     loadAnime()
//     setMounted(true)
//   }, [])
//
//   // Grid animation
//   useEffect(() => {
//     if (!mounted || !animeLoaded || !window.anime || !gridRef.current) return
//
//     const options = {
//       grid: [13, 13],
//       from: "center",
//     }
//
//     const animation = window.anime.timeline({ loop: true })
//       .add({
//         targets: ".orbit-dot",
//         scale: window.anime.stagger([1.1, 0.75], options),
//         easing: "easeInOutQuad",
//         duration: 500,
//         delay: window.anime.stagger(200, options),
//       })
//
//     return () => {
//       if (animation && animation.pause) {
//         animation.pause()
//       }
//     }
//   }, [mounted, animeLoaded])
//
//   if (!mounted) return null
//
//   return (
//     <div className={`flex flex-col items-center space-y-4 ${className}`}>
//       {/* Orbit Grid */}
//       <div ref={gridRef} className="relative">
//         <div className="grid grid-cols-13 gap-1 w-fit mx-auto p-4">
//           {Array.from({ length: 13 * 13 }).map((_, i) => (
//             <div
//               key={i}
//               className="orbit-dot w-1.5 h-1.5 rounded-full bg-emerald-400/60 opacity-30 transition-all duration-300"
//             />
//           ))}
//         </div>
//
//         {/* Central glow effect */}
//         <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
//           <div className="w-8 h-8 rounded-full bg-emerald-400/20 blur-sm animate-pulse" />
//         </div>
//       </div>
//
//       {/* Status Text */}
//       <div className="flex items-center space-x-2">
//         <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" />
//         <span className="text-sm font-medium text-muted-foreground">
//           Orbit is online and completing work
//         </span>
//       </div>
//     </div>
//   )
// }
//
// // Add custom Tailwind class for 13 columns
// // This would need to be added to tailwind.config.js if not already present
// declare module "tailwindcss" {
//   interface Config {
//     theme: {
//       extend: {
//         gridTemplateColumns: {
//           '13': 'repeat(13, minmax(0, 1fr))'
//         }
//       }
//     }
//   }
// }

// export function OrbitGridAnimation({className}: OrbitGridAnimationProps) {
//   const gridRef = useRef<HTMLDivElement>(null)
//   const [animeModule, setAnimeModule] = useState<any>(null)
//   const [mounted, setMounted] = useState(false)

//   // Load anime.js dynamically
//   useEffect(() => {
//     const loadAnime = async () => {
//       try {
//         const animeModule = await import('animejs')
//         const anime = (animeModule as any).default || animeModule
//         setAnimeModule(anime)
//       } catch (error) {
//         console.warn('Failed to load anime.js:', error)
//       }
//     }
//     loadAnime()
//     setMounted(true)
//   }, [])

//   // Grid animation
//   useEffect(() => {
//     if (!mounted || typeof animeModule !== "function" || !gridRef.current) return

//     const staggerOptions = {
//       grid: [13, 13],
//       from: "center",
//     }

//     // Create animation directly without timeline
//     const animation = animeModule({
//       targets: ".orbit-dot",
//       scale: [
//         {value: animeModule.stagger([1.1, 0.75], staggerOptions)}
//       ],
//       easing: "easeInOutQuad",
//       duration: 500,
//       delay: animeModule.stagger(200, staggerOptions),
//       loop: true,
//       direction: 'alternate',
//       autoplay: true
//     })

//     return () => {
//       if (animation && animation.pause) {
//         animation.pause()
//       }
//     }
//   }, [mounted, animeModule])

//   if (!mounted) return null

//   return (
//     <div className={`flex flex-col items-center space-y-4 ${className}`}>
//       {/* Orbit Grid */}
//       <div ref={gridRef} className="relative">
//         <div className="mx-auto grid w-fit grid-cols-13 gap-1 p-4">
//           {Array.from({length: 13 * 13}).map((_, i) => (
//             <div
//               key={i}
//               className="orbit-dot size-1.5 rounded-full bg-emerald-400/60 opacity-30 transition-all duration-300"
//             />
//           ))}
//         </div>

//         {/* Central glow effect */}
//         <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
//           <div className="size-8 animate-pulse rounded-full bg-emerald-400/20 blur-sm"/>
//         </div>
//       </div>

//       {/* Status Text */}
//       <div className="flex items-center space-x-2">
//         <div className="size-2 animate-pulse rounded-full bg-emerald-400"/>
//         <span className="text-sm font-medium text-muted-foreground">
//           Orbit is online and completing work
//         </span>
//       </div>
//     </div>
//   )
// }

const DOTS = 13 // You can play with this, but keep odd for a center

export function OrbitGridAnimation({ className = "" }) {
  const gridRef = useRef<HTMLDivElement>(null)
  const [animeModule, setAnimeModule] = useState<any>(null)
  const [mounted, setMounted] = useState(false)

  // Dynamic import
  useEffect(() => {
    const loadAnime = async () => {
      try {
        const animeImport = await import('animejs')
        // Handles both ESM and CommonJS (sometimes the function is at default, sometimes at root)
        const anime = typeof animeImport === "function"
          ? animeImport
          : (animeImport as any).default || animeImport
        setAnimeModule(anime)
      } catch (error) {
        console.warn('Failed to load anime.js:', error)
      }
    }
    loadAnime()
    setMounted(true)
  }, [])

  // Animation
  useEffect(() => {
    if (!mounted || !animeModule || !gridRef.current) return

    animeModule({
      targets: ".orbit-dot",
      scale: [
        { value: animeModule.stagger([1.15, 0.85], { grid: [DOTS, DOTS], from: "center" }) }
      ],
      opacity: [
        { value: animeModule.stagger([0.92, 0.6], { grid: [DOTS, DOTS], from: "center" }) }
      ],
      easing: "easeInOutSine",
      duration: 1100,
      delay: animeModule.stagger(60, { grid: [DOTS, DOTS], from: "center" }),
      loop: true,
      direction: "alternate",
      autoplay: true
    })
  }, [mounted, animeModule])

  // Helper: generate only dots inside a circle
  const dots: JSX.Element[] = []
  const R = (DOTS - 1) / 2
  for (let y = 0; y < DOTS; y++) {
    for (let x = 0; x < DOTS; x++) {
      const dx = x - R
      const dy = y - R
      if (dx * dx + dy * dy <= R * R + 0.3) { // circle equation, +0.3 for rounding
        dots.push(
          <div
            key={`${x}-${y}`}
            className="orbit-dot w-2 h-2 rounded-full shadow-[0_0_8px_2px_#00ffc3] bg-emerald-300/80 transition-all duration-300"
            style={{
              gridColumn: x + 1,
              gridRow: y + 1,
              zIndex: Math.abs(dx) + Math.abs(dy) === 0 ? 2 : 1,
              // Optional: more intense color/glow for center
              boxShadow: dx === 0 && dy === 0
                ? "0 0 24px 10px #00ffc3, 0 0 60px 0px #0fffd8"
                : "0 0 12px 2px #00ffc3"
            }}
          />
        )
      }
    }
  }

  return (
    <div className={`relative flex flex-col items-center ${className}`}>
      {/* Concentric SVG rings behind */}
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        viewBox="0 0 160 160"
        style={{ zIndex: 0 }}
      >
        <circle cx="80" cy="80" r="70" stroke="#05ffb0" strokeWidth="1.5" fill="none" opacity="0.35"/>
        <circle cx="80" cy="80" r="55" stroke="#05ffb0" strokeWidth="1.5" fill="none" opacity="0.25"/>
        <circle cx="80" cy="80" r="40" stroke="#05ffb0" strokeWidth="1" fill="none" opacity="0.12"/>
        {/* You can animate arc paths here for even more effect */}
      </svg>
      {/* Dots in a circular grid */}
      <div
        ref={gridRef}
        className="relative grid"
        style={{
          gridTemplateColumns: `repeat(${DOTS}, 1fr)`,
          gridTemplateRows: `repeat(${DOTS}, 1fr)`,
          width: 160,
          height: 160,
          zIndex: 1,
        }}
      >
        {dots}
      </div>
      {/* Central pulse */}
      <div className="pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full bg-emerald-400/15 blur-2xl animate-pulse" style={{ zIndex: 2 }} />
    </div>
  )
}