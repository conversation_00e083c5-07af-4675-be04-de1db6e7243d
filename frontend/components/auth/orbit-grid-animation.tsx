"use client"

import { useEffect, useRef, useState } from "react"

interface OrbitGridAnimationProps {
  className?: string
}

export function OrbitGridAnimation({ className }: OrbitGridAnimationProps) {
  const gridRef = useRef<HTMLDivElement>(null)
  const [animeModule, setAnimeModule] = useState<any>(null)
  const [mounted, setMounted] = useState(false)

  // Load anime.js dynamically
  useEffect(() => {
    const loadAnime = async () => {
      try {
        const anime = await import('animejs')
        const animeLib = (anime as any).default || anime
        setAnimeModule(animeLib)
      } catch (error) {
        console.warn('Failed to load anime.js:', error)
      }
    }
    loadAnime()
    setMounted(true)
  }, [])

  // Grid animation
  useEffect(() => {
    if (!mounted || !animeModule || !gridRef.current) return

    const staggerOptions = {
      grid: [13, 13],
      from: "center",
    }

    // Create animation directly without timeline
    const animation = animeModule({
      targets: ".orbit-dot",
      scale: [
        { value: animeModule.stagger([1.1, 0.75], staggerOptions) }
      ],
      easing: "easeInOutQuad",
      duration: 500,
      delay: animeModule.stagger(200, staggerOptions),
      loop: true,
      direction: 'alternate',
      autoplay: true
    })

    return () => {
      if (animation && animation.pause) {
        animation.pause()
      }
    }
  }, [mounted, animeModule])

  if (!mounted) return null

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* Orbit Grid */}
      <div ref={gridRef} className="relative">
        <div className="grid grid-cols-13 gap-1 w-fit mx-auto p-4">
          {Array.from({ length: 13 * 13 }).map((_, i) => (
            <div
              key={i}
              className="orbit-dot w-1.5 h-1.5 rounded-full bg-emerald-400/60 opacity-30 transition-all duration-300"
            />
          ))}
        </div>
        
        {/* Central glow effect */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="w-8 h-8 rounded-full bg-emerald-400/20 blur-sm animate-pulse" />
        </div>
      </div>

      {/* Status Text */}
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" />
        <span className="text-sm font-medium text-muted-foreground">
          Orbit is online and completing work
        </span>
      </div>
    </div>
  )
}

// Note: grid-cols-13 class is defined in tailwind.config.js 