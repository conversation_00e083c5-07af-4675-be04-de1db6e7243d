"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface AuthCardProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
}

export function AuthCard({ children, title, description, className }: AuthCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={cn("w-full", className)}
    >
      <Card className="border-0 bg-white/70 dark:bg-black/70 backdrop-blur-2xl shadow-2xl shadow-black/5 dark:shadow-white/5">
        {(title || description) && (
          <CardHeader className="space-y-1 pb-4">
            {title && (
              <CardTitle className="text-2xl font-semibold tracking-tight text-center">
                {title}
              </CardTitle>
            )}
            {description && (
              <CardDescription className="text-center text-muted-foreground">
                {description}
              </CardDescription>
            )}
          </CardHeader>
        )}
        <CardContent className="space-y-6">
          {children}
        </CardContent>
      </Card>
    </motion.div>
  )
}
