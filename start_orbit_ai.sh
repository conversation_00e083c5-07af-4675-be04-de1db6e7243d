#!/bin/bash

# TractionX Orbit AI Multi-Mode Startup Script
# This script sets up and starts the Orbit AI system with all three modes

echo "🚀 Starting TractionX Orbit AI Multi-Mode System"
echo "================================================"

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Error: Please run this script from the TractionX root directory"
    echo "   Expected structure: ./backend/ and ./frontend/"
    exit 1
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo "🔍 Checking dependencies..."

if ! command_exists python3; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

if ! command_exists node; then
    echo "❌ Node.js is required but not installed"
    exit 1
fi

if ! command_exists npm; then
    echo "❌ npm is required but not installed"
    exit 1
fi

if ! command_exists poetry; then
    echo "❌ Poetry is required but not installed"
    echo "   Install it with: curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

echo "✅ All dependencies found"

# Check environment variables
echo "🔧 Checking environment variables..."

if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  Warning: OPENAI_API_KEY not set (Chat mode will not work)"
    echo "   Set it with: export OPENAI_API_KEY=your_key_here"
fi

if [ -z "$PERPLEXITY_API_KEY" ]; then
    echo "⚠️  Warning: PERPLEXITY_API_KEY not set (Research mode will not work)"
    echo "   Set it with: export PERPLEXITY_API_KEY=your_key_here"
fi

# Setup backend
echo "🐍 Setting up backend..."
cd backend

# Install dependencies with Poetry
echo "📥 Installing Python dependencies with Poetry..."
poetry install --no-dev

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit backend/.env with your API keys and database settings"
fi

# Start backend in background
echo "🚀 Starting backend server..."
poetry run python -m uvicorn app.main:app --reload --port 8000 &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Check if backend is running
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend is running on http://localhost:8000"
else
    echo "❌ Backend failed to start"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# Setup frontend
echo "⚛️  Setting up frontend..."
cd ../frontend

# Install dependencies
echo "📥 Installing Node.js dependencies..."
npm install --silent

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local file..."
    echo "NEXT_PUBLIC_API_URL=http://localhost:8000" > .env.local
fi

# Start frontend
echo "🚀 Starting frontend server..."
npm run dev &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 10

# Check if frontend is running
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend is running on http://localhost:3000"
else
    echo "❌ Frontend failed to start"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 1
fi

# Success message
echo ""
echo "🎉 TractionX Orbit AI Multi-Mode System is now running!"
echo "================================================"
echo ""
echo "📍 Services:"
echo "   Backend:  http://localhost:8000"
echo "   Frontend: http://localhost:3000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "🤖 Orbit AI Modes:"
echo "   💬 Chat Mode:     Fast, human-like responses (OpenAI GPT-4o)"
echo "   🔍 Research Mode: Analyst-grade with sources (Perplexity Sonar-Pro)"
echo "   🤖 Agent Mode:    Autonomous analysis (Coming Soon)"
echo ""
echo "🔧 Configuration:"
echo "   Backend config:  backend/.env"
echo "   Frontend config: frontend/.env.local"
echo ""
echo "📋 To test the system:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Navigate to any deal page"
echo "   3. Click the floating Orbit AI icon"
echo "   4. Switch between Chat and Research modes"
echo "   5. Try the contextual prompts or ask your own questions"
echo ""
echo "🧪 To run tests:"
echo "   python test_orbit_ai.py"
echo ""
echo "⏹️  To stop the system:"
echo "   Press Ctrl+C or run: kill $BACKEND_PID $FRONTEND_PID"
echo ""

# Keep script running and handle cleanup
cleanup() {
    echo ""
    echo "🛑 Shutting down TractionX Orbit AI..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo "✅ Shutdown complete"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Wait for user to stop
echo "💡 System is running. Press Ctrl+C to stop."
wait
