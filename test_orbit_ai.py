#!/usr/bin/env python3
"""
Test script for Orbit AI Multi-Mode Chat Assistant

This script tests the OpenAI, Perplexity integration and multi-mode chat functionality.
Run this from the project root directory.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_openai_client():
    """Test the OpenAI client directly."""
    print("🧪 Testing OpenAI Client (Chat Mode)...")

    try:
        from app.services.openai.client import OpenAIClient

        # Check if API key is set
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ OPENAI_API_KEY not set in environment")
            return False

        client = OpenAIClient(api_key)

        # Test messages
        messages = [
            {"role": "system", "content": "You are Orbit, a helpful investment assistant."},
            {"role": "user", "content": "What are the key factors to consider when evaluating a SaaS startup?"}
        ]

        print("📡 Sending test message to OpenAI...")
        completion_data = await client.create_chat_completion(messages)

        # Extract content and performance
        content = client.extract_content_from_completion(completion_data)
        performance = client.extract_performance_metadata(completion_data)

        print(f"✅ Received response: {content[:100]}...")
        print(f"⚡ Response time: {performance.get('response_time_ms')}ms")
        print(f"🤖 Model: {performance.get('model')}")

        return True

    except Exception as e:
        print(f"❌ OpenAI test failed: {e}")
        return False

async def test_perplexity_client():
    """Test the Perplexity client directly."""
    print("\n🧪 Testing Perplexity Client (Research Mode)...")

    try:
        from app.services.perplexity.client import PerplexityClient

        # Check if API key is set
        api_key = os.getenv("PERPLEXITY_API_KEY")
        if not api_key:
            print("❌ PERPLEXITY_API_KEY not set in environment")
            return False

        client = PerplexityClient(api_key)

        # Test messages
        messages = [
            {"role": "system", "content": "You are Orbit, an AI research analyst. Provide sourced analysis."},
            {"role": "user", "content": "Show me recent funding rounds in fintech with sources"}
        ]

        print("📡 Sending test message to Perplexity...")
        completion_data = await client.create_chat_completion(messages, model="sonar-pro")

        # Extract content and sources
        content = client.extract_content_from_completion(completion_data)
        sources = client.extract_sources_from_completion(completion_data)

        print(f"✅ Received response: {content[:100]}...")
        print(f"📚 Found {len(sources)} sources")

        for i, source in enumerate(sources[:3]):  # Show first 3 sources
            print(f"   {i+1}. {source.title} - {source.url}")

        return True

    except Exception as e:
        print(f"❌ Perplexity test failed: {e}")
        return False

async def test_chat_service():
    """Test the multi-mode chat service."""
    print("\n🧪 Testing Multi-Mode Chat Service...")

    try:
        from app.services.chat.service import ChatService
        from app.schemas.chat import SendMessageRequest
        from app.models.chat import ChatMode
        from motor.motor_asyncio import AsyncIOMotorClient

        # Mock database connection
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.test_tractionx

        service = ChatService(db)

        # Test Chat mode
        print("💬 Testing Chat Mode (OpenAI)...")
        chat_thread = await service.get_or_create_thread(
            deal_id="507f1f77bcf86cd799439011",  # Mock ObjectId
            user_id="507f1f77bcf86cd799439012",  # Mock ObjectId
            org_id="507f1f77bcf86cd799439013",   # Mock ObjectId
            mode=ChatMode.CHAT
        )

        chat_request = SendMessageRequest(
            message="What makes a good investment opportunity?",
            mode=ChatMode.CHAT,
            include_deal_context=False
        )

        chat_message = await service.send_message(
            thread_id=str(chat_thread.id),
            user_id="507f1f77bcf86cd799439012",
            request=chat_request
        )

        print(f"✅ Chat response: {chat_message.content[:100]}...")
        print(f"🤖 Model: {chat_message.ai_model} ({chat_message.ai_provider})")

        # Test Research mode
        print("\n🔍 Testing Research Mode (Perplexity)...")
        research_thread = await service.get_or_create_thread(
            deal_id="507f1f77bcf86cd799439011",  # Mock ObjectId
            user_id="507f1f77bcf86cd799439012",  # Mock ObjectId
            org_id="507f1f77bcf86cd799439013",   # Mock ObjectId
            mode=ChatMode.RESEARCH
        )

        research_request = SendMessageRequest(
            message="Show me recent funding rounds in fintech with sources",
            mode=ChatMode.RESEARCH,
            include_deal_context=False
        )

        research_message = await service.send_message(
            thread_id=str(research_thread.id),
            user_id="507f1f77bcf86cd799439012",
            request=research_request
        )

        print(f"✅ Research response: {research_message.content[:100]}...")
        print(f"🤖 Model: {research_message.ai_model} ({research_message.ai_provider})")
        print(f"📚 Sources: {len(research_message.sources)}")

        # Test Agent mode
        print("\n🤖 Testing Agent Mode...")
        agent_thread = await service.get_or_create_thread(
            deal_id="507f1f77bcf86cd799439011",  # Mock ObjectId
            user_id="507f1f77bcf86cd799439012",  # Mock ObjectId
            org_id="507f1f77bcf86cd799439013",   # Mock ObjectId
            mode=ChatMode.AGENT
        )

        agent_request = SendMessageRequest(
            message="Run autonomous deal analysis",
            mode=ChatMode.AGENT,
            include_deal_context=False
        )

        agent_message = await service.send_message(
            thread_id=str(agent_thread.id),
            user_id="507f1f77bcf86cd799439012",
            request=agent_request
        )

        print(f"✅ Agent response: {agent_message.content[:100]}...")
        print(f"🤖 Model: {agent_message.ai_model} ({agent_message.ai_provider})")

        return True

    except Exception as e:
        print(f"❌ Multi-mode chat service test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Orbit AI Multi-Mode Tests\n")

    # Test 1: OpenAI Client
    openai_ok = await test_openai_client()

    # Test 2: Perplexity Client
    perplexity_ok = await test_perplexity_client()

    # Test 3: Multi-Mode Chat Service (only if at least one AI client works)
    chat_ok = False
    if openai_ok or perplexity_ok:
        chat_ok = await test_chat_service()

    # Summary
    print("\n📊 Test Results:")
    print(f"   OpenAI Client (Chat): {'✅ PASS' if openai_ok else '❌ FAIL'}")
    print(f"   Perplexity Client (Research): {'✅ PASS' if perplexity_ok else '❌ FAIL'}")
    print(f"   Multi-Mode Chat Service: {'✅ PASS' if chat_ok else '❌ FAIL'}")

    if openai_ok and perplexity_ok and chat_ok:
        print("\n🎉 All tests passed! Orbit AI Multi-Mode is ready to go.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

        if not openai_ok:
            print("\n💡 To fix OpenAI issues:")
            print("   1. Set OPENAI_API_KEY in your environment")
            print("   2. Ensure you have a valid OpenAI account with API access")
            print("   3. Check your internet connection")

        if not perplexity_ok:
            print("\n💡 To fix Perplexity issues:")
            print("   1. Set PERPLEXITY_API_KEY in your environment")
            print("   2. Ensure you have a valid Perplexity account")
            print("   3. Check your internet connection")

if __name__ == "__main__":
    asyncio.run(main())
